#include "util/common.h"
#include "receiver/UserDefined.h"
#include "receiver/GalReceiver.h"
#include "UserDefinedCallbacks.h"

static jclass userDefinedClass;
static jfieldID fidNativeUserDefinedEndpoint;
static jfieldID callbackID;
static jobject userDefinedCallbacks;

UserDefined *getUserDefined(JNIEnv *env, jobject jthis) {
    return (UserDefined *) env->GetLongField(jthis, fidNativeUserDefinedEndpoint);
}

void setUserDefined(JNIEnv *env, jobject jthis, UserDefined *userDefined) {
    env->SetLongField(jthis, fidNativeUserDefinedEndpoint, (jlong) userDefined);
}

extern "C" JNIEXPORT jint
Java_com_link_riderservice_feature_cast_autolink_project_UserDefined_nativeInit(JNIEnv *env,
                                                                                     jobject jthis,
                                                                                     jint id,
                                                                                     jlong nativeGalReceiver) {
    userDefinedClass = env->GetObjectClass(jthis);
    fidNativeUserDefinedEndpoint =
            env->GetFieldID(userDefinedClass, "nativeInstance", "J");
    callbackID = env->GetFieldID(userDefinedClass, "callbacks",
                                 "Lcom/link/riderservice/feature/cast/autolink/project/UserDefinedCallbacks;");
    userDefinedCallbacks = env->GetObjectField(jthis, callbackID);
    UserDefined *userDefined = getUserDefined(env, jthis);
    if (userDefined != nullptr) {
        jclass eclass = env->FindClass("java/lang/IllegalStateException");
        env->ThrowNew(eclass, "Already initialized.");
        return -1;  // This return value is ignored.
    }

    auto *galReceiver = (GalReceiver *) nativeGalReceiver;
    userDefined = new UserDefined(static_cast<uint8_t>(id), galReceiver->messageRouter());
    setUserDefined(env, jthis, userDefined);

    shared_ptr<IUserDefinedCallbacks> callbacks(new UserDefinedCallbacks(env, userDefinedCallbacks));
    userDefined->registerCallbacks(callbacks);

    return STATUS_SUCCESS;
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_feature_cast_autolink_project_UserDefined_nativeShutdown
        (JNIEnv *env, jobject jthis) {
    UserDefined *userDefined = getUserDefined(env, jthis);
    if (userDefined != nullptr) {
        userDefined->forceCloseChannel();
        delete userDefined;
        setUserDefined(env, jthis, nullptr);
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_feature_cast_autolink_project_UserDefined_nativeRawDataRequest
        (JNIEnv *env, jobject jthis, jbyteArray msg) {
    UserDefined *userDefined = getUserDefined(env, jthis);
    if (userDefined != nullptr && msg != nullptr) {
        jbyte *cbuf = env->GetByteArrayElements(msg, nullptr);
        jsize len = env->GetArrayLength(msg);
        userDefined->sendRawDataRequest(cbuf, len);

        env->ReleaseByteArrayElements(msg, cbuf, JNI_ABORT);
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_feature_cast_autolink_project_UserDefined_nativeRawDataResponse
        (JNIEnv *env, jobject jthis) {
    UserDefined *userDefined = getUserDefined(env, jthis);
    if (userDefined != nullptr) {
        userDefined->sendRawDataResponse();
    }
}
