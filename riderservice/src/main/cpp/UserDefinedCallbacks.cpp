// Copyright 2014 Google Inc. All Rights Reserved.

#include "UserDefinedCallbacks.h"


int UserDefinedCallbacks::onChannelOpened() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(mJthis, mChannelOpenCallbackId);
}

void UserDefinedCallbacks::onRawDataRequest(void *data, size_t len) {
    JNIEnv *env = JniUtil::getEnv(mVm);

    // 直接从原始数据创建Java字节数组，避免额外拷贝
    jbyteArray javaByteArray = env->NewByteArray(static_cast<jsize>(len));
    env->SetByteArrayRegion(javaByteArray, 0, static_cast<jsize>(len),
                           reinterpret_cast<const jbyte*>(data));

    // 调用Java回调方法
    env->CallVoidMethod(mJthis, mRawDataRequestCallbackId, javaByteArray);

    // 释放本地引用
    env->DeleteLocalRef(javaByteArray);
}

void UserDefinedCallbacks::onRawDataResponse() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mRawDataResponseCallbackId);
}
