// Copyright 2014 Google Inc. All Rights Reserved.
#include <cstdio>
#include "Log.h"
#include "Thread.h"

void *Thread::callback(void *arg) {
    auto *thread = (Thread *) arg;
    thread->run();
    return nullptr;
}

Thread::~Thread() {
    if (mRunning) {
        LOGW("Thread %lx went out of scope while running!", (unsigned long) mThread);
    }
}

bool Thread::start() {
    int ret = pthread_create(&mThread, nullptr, callback, this);
    mRunning = (ret == 0);
    return mRunning;
}

bool Thread::join() {
    if (!mRunning) {
        return false;
    }
    int ret = pthread_join(mThread, nullptr);
    if (ret == 0) {
        mRunning = false;
        return true;
    }
    return false;
}

unsigned long Thread::id() {
    return (unsigned long) mThread;
}

void Thread::yield() {
    sched_yield();
}

bool Thread::setPriority(int priority) {
    struct sched_param params{};
    params.sched_priority = priority;
    int ret = pthread_setschedparam(mThread, SCHED_FIFO, &params);
    fprintf(stderr, "AndroidAuto set thread priority. ret = %d , priority = %d", ret, priority);
    return ret == 0;
}

bool Thread::setName(const char *name) {
    return true;
    //return pthread_setname_np(mThread, name) == 0;
}
