#ifndef AUTOLINK_PROTOCOL_CIRCULAR_BUFFER_H
#define AUTOLINK_PROTOCOL_CIRCULAR_BUFFER_H

#include <cstddef>
#include <stdint.h>
#include <string.h>

/**
 * A simple implementation of a circular buffer.
 */
class CircularBuffer {
public:
    /**
     * Creates a circular buffer of the specified size.
     * @param size The maximum number of bytes that can be stored in this buffer.
     */
    CircularBuffer(size_t size)
            : mBufSize(size), mBufLen(0), mBufStart(0), mBuffer(new uint8_t[size]) {}

    /**
     * Destroys the circular buffer.
     */
    ~CircularBuffer() {
        delete[] mBuffer;
    }

    /**
     * Try to store bytes into the circular buffer.
     * @param buf Pointer to the data to be stored.
     * @param len The number of bytes to be stored.
     * @return The actual number of bytes that were stored.
     */
    size_t put(void *buf, size_t len);

    /**
     * Try to get bytes from the circular buffer.
     * @param buf The memory to read into.
     * @param len The number of bytes to read.
     * @return The number of bytes read.
     */
    size_t get(void *buf, size_t len);

    /**
     * Test if the circular buffer contains data.
     * @return true if it contains data, false otherwise.
     */
    bool empty() { return mBufLen == 0; }

private:
    int mBufSize;
    int mBufLen;
    int mBufStart;
    uint8_t *mBuffer;
};

#endif
