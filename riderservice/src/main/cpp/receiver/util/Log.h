#ifndef AUTOLINK_PROTOCOL_LOG_H
#define AUTOLINK_PROTOCOL_LOG_H

#define GAL_RECEIVER_LOG_TAG            "GalReceiver"

#ifdef __ANDROID__

#include <android/log.h>

#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, GAL_RECEIVER_LOG_TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, GAL_RECEIVER_LOG_TAG, __VA_ARGS__)
#define LOG(...) __android_log_print(ANDROID_LOG_ERROR, GAL_RECEIVER_LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, GAL_RECEIVER_LOG_TAG, __VA_ARGS__)

#elif __QNXNTO__

#include <sys/slog.h>
#include <sys/slogcodes.h>

#define LOGE(...) slogf(_SLOG_SETCODE(_SLOGC_TEST, 1), _SLOG_ERROR,__VA_ARGS__)
#define LOGW(...) slogf(_SLOG_SETCODE(_SLOGC_TEST, 1), _SLOG_WARNING,__VA_ARGS__)
#define LOG(...) slogf(_SLOG_SETCODE(_SLOGC_TEST, 1), _SLOG_INFO,__VA_ARGS__)
#define LOGD(...) slogf(_SLOG_SETCODE(_SLOGC_TEST, 1), _SLOG_DEBUG1,__VA_ARGS__)

#else

#include <stdio.h>
/*
#define LOGE(...) fprintf(stderr, __VA_ARGS__), fprintf(stderr, "\n")
#define LOGW(...) fprintf(stderr, __VA_ARGS__), fprintf(stderr, "\n")
#define LOG(...) fprintf(stderr, __VA_ARGS__), fprintf(stderr, "\n")
#define LOGD(...) fprintf(stderr, __VA_ARGS__), fprintf(stderr, "\n")*/

#define LOGE  printf
#define LOGW  printf
#define LOG   printf
#define LOGD  printf


#endif


#endif