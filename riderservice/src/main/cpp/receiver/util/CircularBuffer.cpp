#include "CircularBuffer.h"

size_t CircularBuffer::put(void *buf, size_t len) {
    if (mBuffer == NULL) {
        return 0;
    }

    int remaining = mBufSize - mBufLen;
    int maxLen = (int64_t) len < remaining ? len : remaining;
    int bytesToCopy = maxLen;
    uint8_t *ptr = (uint8_t *) buf;

    int writeStart = (mBufStart + mBufLen) % mBufSize;
    if (writeStart + bytesToCopy > mBufSize) {
        int n = mBufSize - writeStart;
        memcpy(mBuffer + writeStart, ptr, n);
        bytesToCopy -= n;
        writeStart = 0;
        mBufLen += n;
        ptr += n;
    }

    if (bytesToCopy > 0) {
        memcpy(mBuffer + writeStart, ptr, bytesToCopy);
        mBufLen += bytesToCopy;
    }
    return maxLen;
}

size_t CircularBuffer::get(void *buf, size_t len) {
    if (mBuffer == NULL) {
        return 0;
    }

    int maxLen = (int64_t) len < mBufLen ? len : mBufLen;
    int bytesToCopy = maxLen;
    uint8_t *ptr = (uint8_t *) buf;

    if (mBufStart + bytesToCopy > mBufSize) {
        int n = mBufSize - mBufStart;
        memcpy(ptr, mBuffer + mBufStart, n);
        bytesToCopy -= n;
        mBufStart = 0;
        mBufLen -= n;
        ptr += n;
    }

    if (bytesToCopy > 0) {
        memcpy(ptr, mBuffer + mBufStart, bytesToCopy);
        mBufStart += bytesToCopy;
        mBufLen -= bytesToCopy;
    }

    return maxLen;
}
