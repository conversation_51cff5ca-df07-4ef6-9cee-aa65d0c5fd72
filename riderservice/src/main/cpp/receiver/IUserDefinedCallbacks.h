// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_I_USER_DEFINED_CALLBACKS_H
#define AUTOLINK_PROTOCOL_I_USER_DEFINED_CALLBACKS_H

#include "util/common.h"

class IUserDefinedCallbacks {
public:
    virtual ~IUserDefinedCallbacks() = default;

    virtual int onChannelOpened() = 0;

    virtual void onRawDataRequest(void *data, size_t len) = 0;

    virtual void onRawDataResponse() = 0;
};

#endif
