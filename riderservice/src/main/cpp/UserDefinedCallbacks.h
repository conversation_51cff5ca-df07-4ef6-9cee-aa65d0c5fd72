// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_USER_DEFINED_CALLBACKS_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_USER_DEFINED_CALLBACKS_H

#include "NativeCallbackWrapper.h"
#include "util/common.h"
#include "IUserDefinedCallbacks.h"


class UserDefinedCallbacks : public NativeCallbackWrapper, public IUserDefinedCallbacks {
public:
    UserDefinedCallbacks(JNIEnv *env, jobject jthis) : NativeCallbackWrapper(env, jthis) {
        mChannelOpenCallbackId = env->GetMethodID(
                mThisClass, "onChannelOpened", "()I");
        mRawDataRequestCallbackId = env->GetMethodID(
                mThisClass, "onRawDataRequest", "([B)V");
        mRawDataResponseCallbackId = env->GetMethodID(
                mThisClass, "onRawDataResponse", "()V");
    }

    ~UserDefinedCallbacks() override = default;

    int onChannelOpened() override;

    void onRawDataRequest(void *data, size_t len) override;

    void onRawDataResponse() override;

private:
    jmethodID mChannelOpenCallbackId;
    jmethodID mRawDataRequestCallbackId;
    jmethodID mRawDataResponseCallbackId;
};

#endif // ANDROID_AUTO_PROJECTION_PROTOCOL_USER_DEFINED_CALLBACKS_H
