syntax = "proto2";
option optimize_for = LITE_RUNTIME;
package com.link.riderservice.protobuf;
option java_package = "com.link.riderservice.protobuf";
option java_outer_classname = "RiderProtocol";

enum NaviMessageId {
  MSG_NAVI_INFO = 1;
  MSG_NAVI_TEXT = 2;
  MSG_LANE_INFO = 3;
  MSG_NAVI_INIT_SUCCESS = 4;
  MSG_NAVI_INIT_FAILED = 5;
  MSG_ARRIVE_DEST = 6;
  MSG_GPS_SIGNAL_WEAK = 7;
  MSG_NAVI_ROUTE_NOTIFY = 8;
  MSG_NAVI_CROSS = 9;
  MSG_NAVI_STOP = 10;
  MSG_NAVI_START = 11;
  MSG_WIFI_INFO_NOTIFICATION = 12;
  MSG_NAVI_REQUEST = 13;
  MSG_NAVI_RESPONSE = 14;
  MSG_NAVI_MODE_CHANGE = 15;
  MSG_WIFI_INFO_REQUEST = 16;
  MSG_WEATHER_INFO_REQUEST = 17;
  MSG_WEATHER_INFO_NOTIFICATION = 18;
  MSG_ANDROID_NOTIFICATION = 19;
  MSG_CONFIG_REQUEST = 20;
  MSG_CONFIG_NOTIFICATION = 21;
  MSG_PROTOCOL_VERSION_REQUEST = 22;
  MSG_PROTOCOL_VERSION_NOTIFICATION = 23;
  MSG_COMPLICANCE_REQUEST = 24;
  MSG_COMPLICANCE_NOTIFICATION = 25;
  MSG_ACTIVE_REQUEST = 26;
  MSG_ACTIVE_NOTIFICATION = 27;
  MSG_AUTHORIZATION_NOTIFICATION = 28;
  MSG_TIME_REQUEST = 29;
  MSG_TIME_NOTIFICATION = 30;
  MSG_AUTOLINK_STATE_NOTIFICATION = 31;
  MSG_AUTOLINK_CONNECT = 32;
  MSG_NAVI_MODE_START = 33;  //开始某种模式
  MSG_NAVI_MODE_START_RESPONSE = 34;  //当前模式已开始回复
  MSG_NAVI_MODE_STOP = 35;//结束当前模式
  MSG_NAVI_MODE_STOP_RESPONSE = 36;//当前模式已结束回复
  MSG_NAVI_MODE_CHANGE_RESPONSE = 37;//模式切换是否成功
  MSG_NAVI_CLOSE_CONNECTION = 38;
  MSG_NAVI_DAY_OR_NIGHT = 39;//切换白天黑夜自动模式
  MSG_NAVI_VERSION_REQUEST= 40;
  MSG_NAVI_VERSION_RESPONSE= 41;
  MSG_BT_USERDEF_RAWDATA_REQUEST = 42;
  MSG_BT_USERDEF_RAWDATA_RESPONSE = 43;
};

enum FragInfo {
  FRAG_CONTINUATION = 0;
  FRAG_FIRST = 1;
  FRAG_LAST = 2;
  FRAG_UNFRAGMENTED = 3;
}

enum NaviMode {
  SIMPLE_NAVI = 0;
  SCREEN_NAVI = 1;
  CRUISE_NAVI = 2;
  MIRROR_NAVI = 3;
  DEFAULT_NAVI = 4;
  LOCK_SCREEN_NAVI = 5;   //巡航模式锁屏，导航模式锁屏
  NO_NAVI = 6;          //无巡航模式时建立连接，无巡航模式时关闭导航，未导航时锁屏
}

enum NaviAlStatus {
  AL_CONNECTED = 0;
  AL_DISCONNECTED = 1;
} 

enum NaviDayOrNight {
  NAVI_AUTO = 0;
  NAVI_DAYTIME = 1;
  NAVI_NIGHT = 2;
} 

enum WifiMode{
  WIFI_P2P = 1;
  WIFI_AP = 2;
}

enum NaviModeChangeResult{
  NAVI_MODE_CHANGE_SUCCESS = 1;
  NAVI_MODE_CHANGE_FAIL = 2;
}

enum MessageStatus {
  STATUS_UNSOLICITED_MESSAGE = 1;
  /** Previous message handled successfully. */
  STATUS_SUCCESS = 0;
  /** Sender and receiver cannot agree on a single version, communication cannot continue. */
  STATUS_NO_COMPATIBLE_VERSION = -1;
  /** Certificate presented was invalid. */
  STATUS_CERTIFICATE_ERROR = -2;
  /** Remote end could not be authenticated. */
  STATUS_AUTHENTICATION_FAILURE = -3;
  /** Service ID specified is not one of the services advertised during service discovery. */
  STATUS_INVALID_SERVICE = -4;
  /** Channel ID specified was invalid. */
  STATUS_INVALID_CHANNEL = -5;
  /** Priority specified was invalid. */
  STATUS_INVALID_PRIORITY = -6;
  /** Internal error, recommend to tear down connection to avoid state inconsistency. */
  STATUS_INTERNAL_ERROR = -7;
  /** Media configuration requested is not supported. */
  STATUS_MEDIA_CONFIG_MISMATCH = -8;
  /** Sensor ID specified is not requested. */
  STATUS_INVALID_SENSOR = -9;
  /** Cannot pair Bluetooth now (another MD might be connected with an active call). */
  /** Keycode reported is not handled by the MD. */
  STATUS_KEYCODE_NOT_BOUND = -18;
  /** Can't tune to station b/c it's invalid. */
  STATUS_RADIO_INVALID_STATION = -19;
  STATUS_INVALID_INPUT = -20;
  /** Error occurred retrieving presets. */
  STATUS_RADIO_STATION_PRESETS_NOT_SUPPORTED = -21;
  /** Radio communication error occurred. */
  STATUS_RADIO_COMM_ERROR = -22;
  /** Command not supported. */
  STATUS_COMMAND_NOT_SUPPORTED = -250;
  /** Internal error code, not sent over the protocol. */
  STATUS_FRAMING_ERROR = -251;
  /** Internal error code, not sent over the protocol. */
  STATUS_UNEXPECTED_MESSAGE = -253;
  /** Internal error code, not sent over the protocol. */
  STATUS_BUSY = -254;
  /** Internal error code, not sent over the protocol. */
  STATUS_NO_MEMORY = -255;
  /** Internal error code, not sent over the protocol. */
  STATUS_PARSE_PROTO_FAIL = -256;
}

enum WeatherImageType{
  WEATHER_IMAGE_XUE = 1;
  WEATHER_IMAGE_LEI = 2;
  WEATHER_IMAGE_SHACHEN = 3;
  WEATHER_IMAGE_WU = 4;
  WEATHER_IMAGE_BINGBAO = 5;
  WEATHER_IMAGE_YUN = 6;
  WEATHER_IMAGE_YU = 7;
  WEATHER_IMAGE_YIN = 8;
  WEATHER_IMAGE_QING = 9;
}

message NaviInfoNotification
{
  /** 自车所在小路段索引*/
  required int32 curLink = 1;
  /** 当前位置所在link上的点索引*/
  required int32 curPoint = 2;
  /** 当前大路索引*/
  required int32 curStep = 3;
  /** 当前路段剩余距离*/
  required int32 curStepRetainDistance = 4;
  /** 当前路段剩余时间*/
  required int32 curStepRetainTime = 5;
  /** 导航类型*/
  required int32 naviType = 6;
  /** 导航转向图标类型*/
  required int32 iconType = 7;
  /** 路线剩余时间*/
  required int32 pathRetainTime = 8;
  /** 路线剩余距离*/
  required int32 pathRetainDistance = 9;
  /** 路径剩余红绿灯数量*/
  required int32 routeRemainLightCount = 10;
  /** 线路ID*/
  required int64 pathId = 11;
  /** 下条路名*/
  required string nextRoadName = 12;
  /** 当前路线名称*/
  required string currentRoadName = 13;
  /** 当前车速*/
  optional int32 currentSpeed = 14;
  /** 转向图标*/
  optional bytes iconBitmap = 15;
  /** 地图类型：默认1：高德，2:百度*/
  optional int32 mapType = 16;
  /** 百度转向标图片名称*/
  optional string turnIconName = 17;
  /** 百度转向类型*/
  optional int32 turnKind = 18;
}

message NaviText
{
  /** 播报类型
   *  CUSTOM_TTS_TEXT
   *  INTERRUPT_CURRENT
   *  NAVI_END_TEXT
   *  NAVI_START_TEXT
   *  NAVI_YAW
   *  NAVIINFO_TEXT
   */
  required int32 type = 1;
  /** 播报文案*/
  required string text = 2;
}

message NaviInitSuccess {}

message NaviInitFailure {}

message ArriveDestination {}

message GpsSignalWeak {
  required bool isWeak = 1;
}

message NaviRouteNotify {
  /** 事件点与当前点的距离 -1无效 0处在事件发生范围内*/
  required int32 distance = 1;
  /** 事件点纬度*/
  required double latitude = 2;
  /** 事件点经度*/
  required double longitude = 3;
  /** 通知类型
   *  AVOID_JAM_AREA
   *  AVOID_RESTRICT_AREA
   *  CHANGE_MAIN_ROUTE
   *  DISPATCH
   *  FORBIDDEN_AREA
   *  GPS_SIGNAL_WEAK
   *  ROAD_CLOSED_AREA
   */
  required int32 notifyType = 4;
  /** 事件的主要原因*/
  required string reason = 5;
  /** 事件点的路名*/
  required string roadName = 6;
  /** 事件原因的补充说明*/
  required string subTitle = 7;
  /** 是否成功避开*/
  required bool isSuccess = 8;
}

message LaneInfo {
  /** 背景车道*/
  repeated int32 backgroundLane = 1;
  /** 前景车道*/
  repeated int32 frontLane = 2;
  /** 车道个数*/
  required int32 laneCount = 3;
}

message NaviCross {
  /** 路口放大图的bitmap*/
  required bytes bitmap = 1;
  /** 图片高度*/
  required int32 height = 2;
  /** 图片宽度*/
  required int32 width = 3;
  /** 放大图格式*/
  required int32 picFormat = 4;
}

message NaviStop {}

message NaviStart {}

message WifiInfoNotification
{
  required bool isSupportWifi = 1;
  optional int32 port = 2;
  optional string address = 3;
  optional string name = 4;
  optional string password = 5;
}

message WifiInfoRequest{
  required WifiMode wifiMode = 1;
  required bool isResetWifi = 2;
}

message NaviRequest
{
  required NaviMode naviMode = 1;
}

message NaviResponse
{
  required NaviMode naviMode = 1;
  required bool isReady = 2;
}

message NaviModeChange
{
  required NaviMode naviMode = 1;
}

message WeatherInfoRequest {}

message WeatherInfoNotification
{
  /** 天气情况 */
  required string wea = 1;
  /** 实况温度 */
  required string tem = 2;
  /** 湿度 */
  required string humidity = 3;
  /** 气压 */
  required string pressure = 4;
  /** 空气质量 */
  optional string air = 5;
  /** 风向 */
  optional string win = 6;
  /** 风力等级 */
  optional string win_speed = 7;
  /** 风速 */
  optional string win_meter = 8;
  /** 白天温度(高温) */
  optional string tem_day = 9;
  /** 夜间温度(低温) */
  optional string tem_night = 10;
  /** 天气标识 */
  optional WeatherImageType wea_img = 11;
  /** 更新时间 */
  optional string update_time = 12;
  /** 星期 */
  optional string week = 13;
  /** 日期 */
  optional string date = 14;
  /** 城市名称 */
  optional string city = 15;
  /** 城市ID */
  optional string cityid = 16;
  /** 今日实时请求次数 */
  optional string nums = 17;
  /** 海拔高度 */
  optional string altitude = 18;
}

message AndroidNotification{
  required string appName = 1;
  required string title = 2;
  required string content = 3;
  optional bytes bitmap = 4;
}

message ConfigRequest {}

message ConfigNotification {
  required bool isSupportDvr = 1;
  required bool isSupportNavi = 2;
  required bool isSupportScreenNavi = 3;
  required bool isSupportWeaNoti = 4;
  required bool isSupportAndroidNoti = 5;
  required bool isSupportCircularScreen = 6;
  optional bool isSupportCruiseNavi = 7;
  optional bool isSupportMirrorScreen = 8;
}

message ProtocolVerRequest {}

message ProtocolVerNotification {
  required int32 major = 1;
  required int32 minor = 2;
}

message ComplianceRequest {
  optional string productkey = 1;
  optional string macAddr = 2;
  optional string UUID = 3;
  optional string time = 4;
  optional string license_sign = 5;
  optional string sign = 6;
}
message ComplianceNotification{
  required int32 result = 1;
}
message ActivateRequest{
  required string macAddr = 1;
  required string productKey = 2;
  required string time = 3;
  required string sign = 4;

}
message ActivateNotification{
  required int32 result = 1;
  optional string UUID = 2;
}
message AuthorizationResult{
  required bool result = 1;
}

message TimeRequest {}

message TimeNotification{
  required string time = 1;
}

message AutoLinkStateNotification {
  required int32 state = 1;
}

message AutoLinkConnect {
  optional string ip = 1;
}

message NaviModeStart {
  required NaviMode naviMode = 1;  //当前开始模式
}

message NaviModeStartResponse {
  required NaviMode naviMode = 1;  //当前模式
}

message NaviModeStop {
  required NaviMode naviMode = 1;//当前结束模式
}

message NaviModeStopResponse {
  required NaviMode naviMode = 1;//当前模式
}

message NaviModeChangeResponse
{
  required NaviModeChangeResult naviModeChangeResult = 1;
}

message NaviCloseConnection {}

message NaviModeDayOrNight {
  required NaviDayOrNight naviDayOrNight = 1;
}

message NaviVersionRequest {}

message NaviVersionResponse {
  required string naviVersion= 1;                      //navigation version
}

enum ProtocolVersion {
  option allow_alias = true;
  MAJOR_VERSION = 1;
  MINOR_VERSION = 1;
}


