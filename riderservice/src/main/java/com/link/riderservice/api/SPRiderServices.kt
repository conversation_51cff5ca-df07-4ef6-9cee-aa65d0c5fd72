package com.link.riderservice.api

import android.annotation.SuppressLint
import android.app.Application
import android.bluetooth.BluetoothDevice
import android.media.projection.MediaProjection
import com.link.riderservice.api.SPRiderServices.Companion.makeSharedInstance
import com.link.riderservice.api.callback.BleListCallback
import com.link.riderservice.api.callback.BleStateCallback
import com.link.riderservice.api.callback.ChangeNaviModeCallback
import com.link.riderservice.api.callback.ConnectionStatusCallback
import com.link.riderservice.api.callback.NaviDayOrNightChangeCallback
import com.link.riderservice.api.callback.PermissionDetectionCallback
import com.link.riderservice.api.callback.PresentationCallback
import com.link.riderservice.api.callback.ReceiveCommonDataCallback
import com.link.riderservice.api.callback.SPRiderServicesExceptionNotiCallback
import com.link.riderservice.api.callback.WifiInfoCallback
import com.link.riderservice.api.dto.Channel
import com.link.riderservice.api.dto.DeviceConfig
import com.link.riderservice.api.dto.RiderMessage
import com.link.riderservice.api.dto.SPWeatherInfo
import com.link.riderservice.api.dto.WifiConnectionMode
import com.link.riderservice.core.logging.LogLevel
import com.link.riderservice.core.logging.LogManager
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logI
import com.link.riderservice.core.utils.CrashHandler
import com.link.riderservice.data.local.ConfigPreferences
import com.link.riderservice.feature.connection.ble.model.BleDevice
import com.link.riderservice.feature.connection.coordinator.ConnectionManager
import com.link.riderservice.feature.messaging.MessageManager


class SPRiderServices private constructor(
    val application: Application,
    private val options: SDKOptions
) {
    private var isLoggingCurrentlyEnabled: Boolean

    init {
        this.isLoggingCurrentlyEnabled = options.enableLogging
        initLogSystem(isLoggingCurrentlyEnabled)
        CrashHandler.init()
        ConnectionManager.init(application)
    }

    private fun initLogSystem(enable: Boolean) {
        LogManager.setLogLevel(if (enable) LogLevel.VERBOSE else LogLevel.WARN)
        logI(TAG, "日志系统已初始化 - ${if (enable) "开发" else "生产"}模式")
    }

    /**
     * 获取 SDK 版本信息
     * 
     * @return SDK 版本号字符串，格式为 "主版本号.次版本号.修订号"
     * @example "1.0"
     */
    fun getSDKVersion(): String {
        return ConnectionManager.getSDKVersion()
    }

    /**
     * 获取通信协议版本信息
     * 
     * @return 通信协议版本字符串，用于设备兼容性检查
     */
    fun getProtocolVersion(): String {
        return ConnectionManager.getProtocolVersion()
    }


    /**
     * 获取设备配置信息
     * 
     * 返回连接设备的功能支持信息，包括：
     * - 是否支持 DVR 功能
     * - 支持的导航模式类型
     * - 投屏导航支持状态
     * - 设备硬件能力信息
     * 
     * @return 设备配置对象，如果设备未连接或配置未同步完成则返回 null
     */
    fun getDeviceConfig(): DeviceConfig? {
        return ConnectionManager.getDeviceConfig()
    }


    /**
     * 动态配置 SDK 日志级别
     * 
     * 运行时修改日志输出级别：
     * - true: 开发模式，输出 VERBOSE 级别及以上所有日志
     * - false: 生产模式，仅输出 WARN 级别及以上日志
     * 
     * @param enable true-开发模式，false-生产模式
     */
    fun configureLogEnable(enable: Boolean) {
        val changed = this.isLoggingCurrentlyEnabled != enable
        this.isLoggingCurrentlyEnabled = enable
        if (changed) {
            initLogSystem(enable)
        }
    }


    /**
     * 获取当前完整的连接状态信息
     * 
     * 包含蓝牙连接状态、WiFi 连接状态、设备信息等
     * 
     * @return 连接状态对象，包含所有连接相关信息
     */
    fun getConnectStatus(): Connection {
        return ConnectionManager.getConnectStatus()
    }

    /**
     * 获取蓝牙连接状态
     * 
     * 仅返回蓝牙部分的连接状态，不包含其他连接信息
     * 
     * @return 蓝牙连接状态枚举值
     */
    fun getBleStatus(): BleStatus {
        return ConnectionManager.getConnectStatus().btStatus
    }

    /**
     * 启动蓝牙快速连接
     * 
     * 使用快速连接模式扫描设备，优先尝试连接上次成功连接的设备
     * 如果上次连接的设备不可用，会自动切换到普通扫描模式
     * 
     * @see startScan
     * @see reconnect
     */
    fun startBleQuickConnect() {
        ConnectionManager.startScan(true)
    }

    /**
     * 开始扫描附近的蓝牙设备
     * 
     * 启动蓝牙扫描，发现附近可用的设备
     * 扫描结果通过 [addBleListCallback] 注册的回调返回
     */
    fun startScan() {
        ConnectionManager.startScan(false)
    }

    /**
     * 停止蓝牙设备扫描
     * 
     * 停止当前的蓝牙扫描过程
     */
    fun stopScan() {
        ConnectionManager.stopScan()
    }


    /**
     * 获取已扫描到的蓝牙设备列表
     * 
     * 返回最近一次扫描发现的蓝牙设备列表
     * 列表中的设备信息包括名称、MAC地址、信号强度等
     * 
     * @return 蓝牙设备列表，如果没有扫描到设备则返回空列表
     */
    fun getBleList(): List<BleDevice> {
        return ConnectionManager.getBleList()
    }

    /**
     * 连接到指定的蓝牙设备
     * 
     * 发起与指定蓝牙设备的连接请求
     * 连接结果通过 [addBleConnectStateCallback] 注册的回调返回
     * 
     * @param device 要连接的蓝牙设备对象，通常从 [getBleList] 获取
     */
    fun connect(device: BleDevice) {
        ConnectionManager.connectBle(device, application)
    }

    /**
     * 断开当前所有连接
     * 
     * 断开蓝牙连接和WiFi连接，恢复到未连接状态
     */
    fun disconnect() {
        ConnectionManager.disconnect()
    }

    /**
     * 清除已配对设备的信息
     * 
     * 清除本地存储的设备配对信息，包括连接配置等
     * 下次连接需要重新进行设备发现和配对
     */
    fun clearPaired() {
        ConfigPreferences.getInstance(application).clearAllConfig()
    }

    /**
     * 尝试重新连接到上次连接的设备
     * 
     * 使用快速连接模式，尝试重新连接到最后一次成功连接的设备
     * 如果设备不在范围内或不可用，会自动切换到普通扫描模式
     */
    fun reconnect() {
        ConnectionManager.startScan(true)
    }

    /**
     * 获取当前连接的蓝牙设备信息
     * 
     * 返回当前已建立连接的蓝牙设备的详细信息
     * 包括设备名称、MAC地址、信号强度等属性
     * 
     * @return 当前连接的蓝牙设备对象，如果未连接则返回 null
     * @see BleDevice
     */
    fun getCurrentBleInfo(): BluetoothDevice? {
        return ConnectionManager.getCurrentConnectDevice()
    }

    /**
     * 使用二维码值连接设备
     * 
     * 通过扫描设备二维码获取的连接信息进行设备连接
     * 二维码通常包含设备的MAC地址、配对密钥等信息
     * 
     * @param value 二维码解析得到的连接信息字符串
     *
     */
    fun connectUseQRCodeValue(value: String) {
        ConnectionManager.connectUseQRCodeValue(value)
    }


    /**
     * 连接设备 WiFi
     * 
     * 启动 WiFi 连接流程，根据当前配置的连接模式进行连接：
     * - WIFI_AP_CLIENT: 连接设备的热点
     * - WIFI_P2P: 建立点对点 WiFi 连接
     * 
     * 连接状态通过 WiFi 信息回调返回
     * 
     * @see setWifiConnectionMode
     * @see addWifiInfoCallback
     */
    fun connectWifi() {
        ConnectionManager.connectWifi()
    }

    /**
     * 获取当前 WiFi 连接信息
     * 
     * 返回当前 WiFi 连接的详细信息，包括：
     * - SSID: 网络名称
     * - 信号强度
     * - 连接状态
     * - IP 地址信息
     * 
     * @return WiFi 信息对象，包含所有连接相关信息
     * @see SPWifiInfo
     */
    fun getWifiInfo():SPWifiInfo {
        return ConnectionManager.getWifiInfo()
    }

    /**
     * 获取当前激活的导航模式
     * 
     * 返回当前正在使用的导航模式，可能是以下之一：
     * - 简易导航模式
     * - 投屏导航模式  
     * - 巡航导航模式
     * - 镜像导航模式
     * - 默认导航模式
     * - 锁屏导航模式
     * - 无导航模式
     * 
     * @return 当前导航模式枚举值
     */
    fun getCurrentNaviMode(): SPNaviMode {
        return ConnectionManager.getCurrentNaviMode()
    }

    /**
     * 获取上一个导航模式
     * 
     * 返回切换前的导航模式，用于实现"返回上一模式"功能
     * 
     * @return 上一个导航模式枚举值
     */
    fun getPreviousNaviMode(): SPNaviMode {
        return ConnectionManager.getPreviousNaviMode()
    }

    /**
     * 检查是否处于镜像模式
     * 
     * 镜像模式将手机屏幕内容实时投射到设备显示
     * 
     * @return true-处于镜像模式，false-非镜像模式
     */
    fun isMirrorMode(): Boolean {
        return ConnectionManager.isMirrorMode()
    }

    /**
     * 检查是否处于巡航模式
     * 
     * 巡航模式提供简化的导航界面，适合长时间驾驶
     * 
     * @return true-处于巡航模式，false-非巡航模式
     */
    fun isCruiseMode(): Boolean {
        return ConnectionManager.isCruiseMode()
    }

    /**
     * 检查是否处于投屏导航模式
     * 
     * 投屏模式将导航界面投射到设备屏幕显示
     * 
     * @return true-处于投屏导航模式，false-非投屏模式
     */
    fun isScreenNaviMode(): Boolean {
        return ConnectionManager.isScreenNaviMode()
    }


    /**
     * 发送通用消息到设备
     * 
     * 向已连接的设备发送自定义协议消息
     * 消息内容遵循 Rider 消息协议格式
     * 
     * @param message 要发送的消息对象
     * @see RiderMessage
     */
    fun sendMessage(message: RiderMessage) {
        if (ConnectionManager.isBleConnected()) {
            MessageManager.sendMessage(message)
        }
    }

    /**
     * 发送通用二进制数据到设备
     * 
     * 向设备发送原始二进制数据，用于扩展功能或自定义协议
     * 数据格式由应用层和设备端协商确定
     * 
     * @param byteArray 要发送的二进制数据字节数组
     * @param channelId 要发送到的通道ID
     */
    fun sendCommonData(byteArray: ByteArray,channelId: Channel = Channel.Bluetooth) {
        if (ConnectionManager.isBleConnected()) {
            if (channelId == Channel.Bluetooth){
                MessageManager.sendCommonData(byteArray)
            }else {
                ConnectionManager.sendCommonData(byteArray)
            }
        }
    }

    /**
     * 发送导航开始消息
     * 
     * 通知设备导航已经开始，设备可以准备显示导航界面
     * 通常在应用启动导航后调用
     */
    fun sendMessageNaviStart() {
        if (ConnectionManager.isBleConnected()) {
            MessageManager.sendMessageNaviStart()
        }
    }

    /**
     * 发送导航停止消息
     * 
     * 通知设备导航已经结束，设备可以关闭导航界面
     * 通常在应用停止导航后调用
     */
    fun sendMessageNaviStop() {
        if (ConnectionManager.isBleConnected()) {
            MessageManager.sendMessageNaviStop()
        }
    }

    /**
     * 发送导航模式请求消息
     * 
     * 请求设备切换到指定的导航模式
     * 设备会根据自身能力决定是否接受模式切换
     * 
     * @param spNaviMode 请求的导航模式
     * @see SPNaviMode
     */
    fun sendMessageNaviRequest(spNaviMode: SPNaviMode) {
        if (ConnectionManager.isBleConnected()) {
            MessageManager.sendMessageNaviRequest(spNaviMode)
        }
    }

    /**
     * 发送天气信息消息
     * 
     * 向设备发送当前天气信息，用于在设备端显示天气状况
     * 包括温度、天气状况、湿度等信息
     * 
     * @param weatherInfo 天气信息对象
     * @see SPWeatherInfo
     */
    fun sendMessageWeatherInfo(weatherInfo: SPWeatherInfo) {
        if (ConnectionManager.isBleConnected()) {
            MessageManager.sendMessageWeatherInfo(weatherInfo)
        }
    }

    /**
     * 启动指定的导航模式
     * 
     * 切换到指定的导航模式，如果设备支持该模式
     * 
     * @param naviMode 要启动的导航模式，支持以下模式：
     * - SPNaviModeSimpleNavi: 简易导航模式
     * - SPNaviModeScreenNavi: 投屏导航模式
     * - SPNaviModeCruiseNavi: 巡航导航模式
     * - SPNaviModeMirrorNavi: 镜像导航模式
     * - SPNaviModeDefaultNavi: 默认导航模式
     * - SPNaviModeLockScreenNavi: 锁屏导航模式
     * - SPNaviModeNoNavi: 无导航模式
     */
    fun startNaviMode(naviMode: SPNaviMode) {
        ConnectionManager.startNaviMode(naviMode)
    }


    /**
     * 改变当前的导航模式
     * 
     * 与 [startNaviMode] 功能相同，提供语义更明确的接口
     * 
     * @param naviMode 要切换到的导航模式
     */
    fun changeNaviMode(naviMode: SPNaviMode) {
        ConnectionManager.changeNaviMode(naviMode)
    }

    /**
     * 停止指定的导航模式
     * 
     * 停止当前运行的指定导航模式
     * 
     * @param naviMode 要停止的导航模式
     */
    fun stopNaviMode(naviMode: SPNaviMode) {
        ConnectionManager.stopNaviMode(naviMode)
    }


    /**
     * 设置 MediaProjection 实例
     * 
     * 用于屏幕镜像功能，必须在启动镜像模式前调用
     * 需要先申请 Android 屏幕录制权限
     * 
     * @param mediaProjection MediaProjection 实例
     */
    fun setMediaProjection(mediaProjection: MediaProjection) {
        ConnectionManager.setMediaProjection(mediaProjection)
    }


    /**
     * 添加蓝牙设备列表更新回调
     * 
     * 注册回调以接收蓝牙设备扫描结果
     * 当 [startScan] 发现新设备时会触发此回调
     * 
     * @param callback 设备列表更新回调接口
     */
    fun addBleListCallback(callback: BleListCallback) {
        ConnectionManager.addBleListCallback(callback)
    }

    /**
     * 移除蓝牙设备列表更新回调
     * 
     * 取消之前注册的设备列表更新回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removeBleListCallback(callback: BleListCallback) {
        ConnectionManager.removeBleListCallback(callback)
    }

    /**
     * 添加蓝牙连接状态回调
     * 
     * 注册回调以接收蓝牙连接状态变化事件
     * 包括连接成功、断开、连接失败等状态
     * 
     * @param callback 蓝牙连接状态回调接口
     */
    fun addBleConnectStateCallback(callback: BleStateCallback) {
        ConnectionManager.addBleConnectStateCallback(callback)
    }

    /**
     * 移除蓝牙连接状态回调
     * 
     * 取消之前注册的蓝牙连接状态回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removeBleConnectStateCallback(callback: BleStateCallback) {
        ConnectionManager.removeBleConnectStateCallback(callback)
    }

    /**
     * 添加 WiFi 信息更新回调
     * 
     * 注册回调以接收 WiFi 连接信息变化
     * 包括 SSID、信号强度、连接状态等信息
     * 
     * @param callback WiFi 信息回调接口
     */
    fun addWifiInfoCallback(callback: WifiInfoCallback) {
        ConnectionManager.addWifiInfoCallback(callback)
    }

    /**
     * 移除 WiFi 信息回调
     * 
     * 取消之前注册的 WiFi 信息更新回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removeWifiInfoCallback(callback: WifiInfoCallback) {
        ConnectionManager.removeWifiInfoCallback(callback)
    }


    /**
     * 添加导航模式变更回调
     * 
     * 注册回调以接收导航模式切换事件
     * 当调用 [startNaviMode] 或设备主动切换模式时触发
     * 
     * @param callback 导航模式变更回调接口
     */
    fun addChangeNaviModeCallback(callback: ChangeNaviModeCallback) {
        ConnectionManager.addChangeNaviModeCallback(callback)
    }

    /**
     * 移除导航模式变更回调
     * 
     * 取消之前注册的导航模式变更回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removeChangeNaviModeCallback(callback: ChangeNaviModeCallback) {
        ConnectionManager.removeChangeNaviModeCallback(callback)
    }

    /**
     * 添加完整连接状态回调
     * 
     * 注册回调以接收完整的连接状态变化
     * 包含蓝牙、WiFi 等所有连接组件的状态信息
     * 
     * @param callback 连接状态回调接口
     */
    fun addConnectionStatusCallback(callback: ConnectionStatusCallback) {
        ConnectionManager.addConnectionStatusCallback(callback)
    }

    /**
     * 移除连接状态回调
     * 
     * 取消之前注册的连接状态回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removeConnectionStatusCallback(callback: ConnectionStatusCallback) {
        ConnectionManager.removeConnectionStatusCallback(callback)
    }

    /**
     * 添加权限检测回调
     * 
     * 注册回调以接收权限需求通知
     * 当 SDK 需要特定权限时（如屏幕录制的 MediaProjection 权限）触发
     * 
     * @param callback 权限检测回调接口
     */
    fun addPermissionDetectionCallback(callback: PermissionDetectionCallback) {
        ConnectionManager.addPermissionDetectionCallback(callback)
    }

    /**
     * 移除权限检测回调
     * 
     * 取消之前注册的权限检测回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removePermissionDetectionCallback(callback: PermissionDetectionCallback) {
        ConnectionManager.removePermissionDetectionCallback(callback)
    }

    /**
     * 添加异常通知回调
     * 
     * 注册回调以接收 SDK 运行时异常通知
     * 包括连接异常、协议错误、设备兼容性问题等
     * 
     * @param callback 异常通知回调接口
     */
    fun addRiderServicesExceptionNotiCallback(callback: SPRiderServicesExceptionNotiCallback) {
        ConnectionManager.addRiderServicesExceptionNotiCallback(callback)
    }

    /**
     * 移除异常通知回调
     * 
     * 取消之前注册的异常通知回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removeRiderServicesExceptionNotiCallback(callback: SPRiderServicesExceptionNotiCallback) {
        ConnectionManager.removeRiderServicesExceptionNotiCallback(callback)
    }

    /**
     * 添加通用数据接收回调
     * 
     * 注册回调以接收设备发送的通用二进制数据
     * 用于扩展功能或自定义协议数据传输
     * 
     * @param callback 通用数据接收回调接口
     */
    fun addReceiveCommonDataCallback(callback: ReceiveCommonDataCallback) {
        ConnectionManager.addReceiveCommonDataCallback(callback)
    }

    /**
     * 移除通用数据接收回调
     * 
     * 取消之前注册的通用数据接收回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removeReceiveCommonDataCallback(callback: ReceiveCommonDataCallback) {
        ConnectionManager.removeReceiveCommonDataCallback(callback)
    }

    /**
     * 添加投屏演示回调
     * 
     * 注册回调以接收投屏相关事件：
     * - 投屏显示准备就绪
     * - 投屏显示被释放
     * - 需要 MediaProjection 权限
     * 
     * @param callback 投屏演示回调接口
     */
    fun addPresentationCallback(callback: PresentationCallback) {
        ConnectionManager.addPresentationCallback(callback)
    }

    /**
     * 移除投屏演示回调
     * 
     * 取消之前注册的投屏演示回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removePresentationCallback(callback: PresentationCallback) {
        ConnectionManager.removePresentationCallback(callback)
    }

    /**
     * 添加导航日夜模式变更回调
     * 
     * 注册回调以接收导航日夜模式切换事件
     * 当设备根据环境光线自动切换日夜模式时触发
     * 
     * @param callback 导航日夜模式变更回调接口
     * @see NaviDayOrNightChangeCallback
     */
    fun addNaviDayOrNightChangeCallback(callback: NaviDayOrNightChangeCallback) {
        ConnectionManager.addNaviDayOrNightChangeCallback(callback)
    }

    /**
     * 移除导航日夜模式变更回调
     * 
     * 取消之前注册的导航日夜模式变更回调
     * 
     * @param callback 要移除的回调接口
     */
    fun removeNaviDayOrNightChangeCallback(callback: NaviDayOrNightChangeCallback) {
        ConnectionManager.removeNaviDayOrNightChangeCallback(callback)
    }


    /**
     * 设置 WiFi 连接模式
     * 
     * 在蓝牙连接前配置 WiFi 连接方式，支持两种模式：
     * - WIFI_AP_CLIENT: 客户端模式，连接设备的热点
     * - WIFI_P2P: 点对点模式，直接设备间连接
     * 
     * @param mode WiFi 连接模式枚举值
     */
    fun setWifiConnectionMode(mode: WifiConnectionMode) {
        ConnectionManager.setWifiConnectionMode(mode)
    }

    /**
     * 获取当前设置的 WiFi 连接模式
     * 
     * @return 当前配置的 WiFi 连接模式
     */
    fun getWifiConnectionMode(): WifiConnectionMode {
        return ConnectionManager.getWifiConnectionMode()
    }


    companion object {
        private const val TAG = "SPRiderServices"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: SPRiderServices? = null

        /**
         * 创建并初始化 SPRiderServices 共享实例
         * 
         * SDK 入口方法，必须在应用启动时调用一次
         * 采用单例模式，后续调用返回同一实例
         * 
         * @param application Application 上下文
         * @param options SDK 初始化选项
         * @return SPRiderServices 单例实例
         * @throws IllegalStateException 如果使用不同的参数重复初始化
         */
        @JvmStatic
        @Synchronized
        fun makeSharedInstance(application: Application, options: SDKOptions): SPRiderServices {
            if (instance == null) {
                instance = SPRiderServices(application, options)
            } else {
                if (instance!!.application.applicationContext !== application.applicationContext || instance!!.options != options) {
                    throw IllegalStateException("SPRiderServices already initialized with different context or options.")
                }
                logD("SPRiderServices.makeSharedInstance called again with the same parameters. Returning existing instance.")
            }
            return instance!!
        }

        /**
         * 获取 SPRiderServices 共享实例
         * 
         * 获取已初始化的 SDK 实例
         * 必须在 [makeSharedInstance] 之后调用
         * 
         * @return SPRiderServices 单例实例
         * @throws IllegalStateException 如果 SDK 尚未初始化
         */
        @JvmStatic
        fun getSharedInstance(): SPRiderServices {
            return instance
                ?: throw IllegalStateException("SPRiderServices not initialized. Call SPRiderServices.makeSharedInstance(context, options) first.")
        }
    }
}
