package com.link.riderservice.api.callback

import com.link.riderservice.feature.connection.ble.model.BleDevice

/**
 * 蓝牙设备列表更新回调接口
 * 
 * 用于接收蓝牙扫描发现的设备列表更新通知
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface BleListCallback {
    
    /**
     * 蓝牙设备列表更新回调方法
     * 
     * 当调用 [com.link.riderservice.api.SPRiderServices.startScan] 或 
     * [com.link.riderservice.api.SPRiderServices.startBleQuickConnect] 扫描设备时，
     * 发现新的蓝牙设备后会触发此回调
     * 
     * @param bleDevices 扫描发现的蓝牙设备列表，包含设备名称、MAC地址、信号强度等信息
     * @see com.link.riderservice.api.SPRiderServices.addBleListCallback
     * @see com.link.riderservice.api.SPRiderServices.removeBleListCallback
     * @see com.link.riderservice.feature.connection.ble.model.BleDevice
     */
    fun onBleListResult(bleDevices: List<BleDevice>)
}