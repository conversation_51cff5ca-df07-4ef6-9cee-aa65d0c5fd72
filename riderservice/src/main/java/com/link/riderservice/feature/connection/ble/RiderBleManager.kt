package com.link.riderservice.feature.connection.ble

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.LocationManager
import com.link.riderservice.api.SPRiderServices
import com.link.riderservice.core.extensions.collectWithScope
import com.link.riderservice.core.extensions.mainScope
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.core.logging.logW
import com.link.riderservice.core.utils.BleUtils
import com.link.riderservice.data.local.ConfigPreferences
import com.link.riderservice.feature.analytics.ConnectionAnalytics
import com.link.riderservice.feature.connection.ble.callback.BleManagerCallback
import com.link.riderservice.feature.connection.ble.core.strategy.CompositeStrategy
import com.link.riderservice.feature.connection.ble.core.strategy.ConfigPreferredStrategy
import com.link.riderservice.feature.connection.ble.core.strategy.DeviceSelectionStrategy
import com.link.riderservice.feature.connection.ble.core.strategy.ManualPreferredStrategy
import com.link.riderservice.feature.connection.ble.core.strategy.NearestRssiStrategy
import com.link.riderservice.feature.connection.ble.internal.impl.BleManagerImpl
import com.link.riderservice.feature.connection.ble.model.BleDevice
import com.link.riderservice.feature.connection.ble.scanning.filter.ScanFilters
import com.link.riderservice.libs.ble.data.Data
import com.link.riderservice.libs.ble.ktx.state
import com.link.riderservice.libs.ble.ktx.state.ConnectionState
import com.link.riderservice.libs.ble.scanner.BluetoothLeScannerCompat
import com.link.riderservice.libs.ble.scanner.ScanCallback
import com.link.riderservice.libs.ble.scanner.ScanFilter
import com.link.riderservice.libs.ble.scanner.ScanResult
import com.link.riderservice.libs.ble.scanner.ScanSettings
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

internal object RiderBleManager {
    // BLE 状态枚举
    enum class BleState {
        IDLE,           // 空闲状态
        SCANNING,       // 扫描中
        CONNECTING,     // 连接中
        CONNECTED,      // 已连接
        DISCONNECTING,  // 断开连接中
        ERROR          // 错误状态
    }

    // BLE 状态监听器接口 - 精简设计
    interface BleStateListener {
        // 设备相关 - ConnectionManager主要使用的
        fun onDeviceConnected(device: BluetoothDevice)
        fun onDeviceDisconnected(device: BluetoothDevice, reason: Int)
        fun onDeviceConnectionFailed(device: BluetoothDevice, reason: Int)

        // 扫描相关
        fun onScanResults(devices: List<BleDevice>)

        // 数据相关
        fun onDataReceived(device: BluetoothDevice, data: Data)

        // 权限和错误相关
        fun onBluetoothDisabled()
        fun onLocationPermissionRequired()
        fun onBluetoothScanPermissionRequired()
        fun onNotificationFailed(device: BluetoothDevice, status: Int)
    }

    // BLE 状态管理器
    private class BleStateManager {
        var currentState: BleState = BleState.IDLE
            private set

        var currentDevice: BluetoothDevice? = null
            private set

        var isAutoConnectionEnabled: Boolean = true
            private set

        var isConfigurationConnected: Boolean = false
            private set

        var discoveredDevices: List<BleDevice> = emptyList()
            private set

        fun updateState(newState: BleState): Boolean {
            return if (canTransitionTo(newState)) {
                currentState = newState
                // 状态变化已经通过具体的回调通知
                true
            } else {
                logW(TAG, "Invalid state transition: $currentState -> $newState")
                false
            }
        }

        fun updateDevice(device: BluetoothDevice?) {
            currentDevice = device
        }

        fun updateDiscoveredDevices(devices: List<BleDevice>) {
            discoveredDevices = devices.toList()
            notifyScanResults?.invoke(devices)
        }

        fun setAutoConnectionEnabled(enabled: Boolean) {
            isAutoConnectionEnabled = enabled
        }

        fun setConfigurationConnected(connected: Boolean) {
            isConfigurationConnected = connected
        }

        private fun canTransitionTo(newState: BleState): Boolean {
            return when (currentState) {
                BleState.IDLE -> newState in listOf(BleState.SCANNING, BleState.CONNECTING)
                BleState.SCANNING -> newState in listOf(BleState.IDLE, BleState.CONNECTING, BleState.ERROR)
                BleState.CONNECTING -> newState in listOf(BleState.CONNECTED, BleState.IDLE, BleState.ERROR)
                BleState.CONNECTED -> newState in listOf(BleState.DISCONNECTING, BleState.ERROR)
                BleState.DISCONNECTING -> newState in listOf(BleState.IDLE, BleState.ERROR)
                BleState.ERROR -> newState in listOf(BleState.IDLE, BleState.SCANNING)
            }
        }

        // 通知方法（由 RiderBleManager 调用）
        var notifyDeviceConnected: ((BluetoothDevice) -> Unit)? = null
        var notifyDeviceDisconnected: ((BluetoothDevice, Int) -> Unit)? = null
        var notifyDeviceConnectionFailed: ((BluetoothDevice, Int) -> Unit)? = null
        var notifyScanResults: ((List<BleDevice>) -> Unit)? = null
        var notifyDataReceived: ((BluetoothDevice, Data) -> Unit)? = null
        var notifyBluetoothDisabled: (() -> Unit)? = null
        var notifyLocationPermissionRequired: (() -> Unit)? = null
        var notifyBluetoothScanPermissionRequired: (() -> Unit)? = null
        var notifyNotificationFailed: ((BluetoothDevice, Int) -> Unit)? = null
    }

    // 状态管理器实例
    private val stateManager = BleStateManager()

    // 状态监听器集合
    private val stateListeners = mutableListOf<WeakReference<BleStateListener>>()

    // 过滤器常量与判定已抽取到 ScanFilters

    // 扫描配置常量
    private const val TIMEOUT_SCAN = 1_200L
    private const val SLICE_SCANNING_PERIOD_MS = 6 * 1000L
    private const val SCANNING_DURATION = 10 * 1000L
    private const val SCHEDULE_START_SCAN_WHEN_STARTED_TOO_FREQUENTLY = true

    private const val TAG = "RiderBleManager"

    private val discoveredBleDevices: MutableList<BleDevice> = ArrayList()
    private var bleManager: BleManagerImpl? = null
    private var lastScanTimestamp: Long = 0
    private var delayScanJob: Job? = null
    private var deviceSelectionStrategy: DeviceSelectionStrategy =
        CompositeStrategy(
            ManualPreferredStrategy(),
            ConfigPreferredStrategy(),
            NearestRssiStrategy()
        )

    // 使用 lazy 属性，避免重复获取
    private val configPrefs by lazy { ConfigPreferences.getInstance(SPRiderServices.getSharedInstance().application) }

    // 初始化状态管理器通知
    init {
        stateManager.notifyDeviceConnected = { device ->
            stateListeners.forEach { it.get()?.onDeviceConnected(device) }
        }
        stateManager.notifyDeviceDisconnected = { device, reason ->
            stateListeners.forEach { it.get()?.onDeviceDisconnected(device, reason) }
        }
        stateManager.notifyDeviceConnectionFailed = { device, reason ->
            stateListeners.forEach { it.get()?.onDeviceConnectionFailed(device, reason) }
        }
        stateManager.notifyScanResults = { devices ->
            stateListeners.forEach { it.get()?.onScanResults(devices) }
        }
        stateManager.notifyDataReceived = { device, data ->
            stateListeners.forEach { it.get()?.onDataReceived(device, data) }
        }
        stateManager.notifyBluetoothDisabled = {
            stateListeners.forEach { it.get()?.onBluetoothDisabled() }
        }
        stateManager.notifyLocationPermissionRequired = {
            stateListeners.forEach { it.get()?.onLocationPermissionRequired() }
        }
        stateManager.notifyBluetoothScanPermissionRequired = {
            stateListeners.forEach { it.get()?.onBluetoothScanPermissionRequired() }
        }
        stateManager.notifyNotificationFailed = { device, status ->
            stateListeners.forEach { it.get()?.onNotificationFailed(device, status) }
        }
    }

    // 状态监听器管理
    fun addStateListener(listener: BleStateListener) {
        stateListeners.add(WeakReference(listener))
        // 立即通知当前状态
        // 不再需要状态变化通知，因为状态通过具体事件体现
    }

    fun removeStateListener(listener: BleStateListener) {
        stateListeners.removeIf { it.get() == listener }
    }

    // 状态查询接口
    fun getCurrentState(): BleState = stateManager.currentState
    fun getCurrentDevice(): BluetoothDevice? = stateManager.currentDevice
    fun isAutoConnectionEnabled(): Boolean = stateManager.isAutoConnectionEnabled
    fun isConfigurationConnected(): Boolean = stateManager.isConfigurationConnected


    fun isConfigConnect(): Boolean {
        return stateManager.isConfigurationConnected
    }

    fun getConnectAddress(): String {
        return stateManager.currentDevice?.address ?: ""
    }

    fun connect(device: BluetoothDevice, context: Context) {
        stopScan()
        // 使用状态管理器进行状态转换
        if (!stateManager.updateState(BleState.CONNECTING)) {
            logW(TAG, "Cannot connect in current state: ${stateManager.currentState}")
            return
        }


        // 检查是否需要断开当前连接
        if (stateManager.currentDevice != null &&
            stateManager.currentDevice?.address != device.address) {
            logD(TAG, "Disconnecting from current device before connecting to new device")
            releaseManager()
        }

        // 更新状态管理器中的设备信息
        stateManager.updateDevice(device)


        // 记录连接开始
        ConnectionAnalytics.startBluetoothConnect()
        bleManager = BleManagerImpl(context, device, createBleManagerCallback())

        bleManager?.state?.let {
            if (it == ConnectionState.Connecting) {
                logD(TAG, "Already connecting, ignoring connect request")
                return
            }
        }

        if (isConnected()) {
            logD(TAG, "Already connected, ignoring connect request")
            return
        }

        bleManager?.connect()
    }

    /**
     * 通过蓝牙MAC地址直接连接设备
     * @param macAddress 蓝牙设备的MAC地址
     * @param context 应用上下文
     * @return 是否成功启动连接
     */
    fun connectByMacAddress(macAddress: String, context: Context): Boolean {
        logD(TAG, "Attempting to connect to device with MAC address: $macAddress")

        try {
            val bluetoothDevice =
                context.getSystemService(BluetoothManager::class.java).adapter.getRemoteDevice(
                    macAddress
                )
            connect(bluetoothDevice, context)
            logD(TAG, "Successfully initiated connection to device: $macAddress")
            return true

        } catch (e: Exception) {
            logE(TAG, "Failed to connect to device with MAC: $macAddress", e)
            return false
        }
    }

    private fun createBleManagerCallback(): BleManagerCallback {
        return object : BleManagerCallback {
            override fun onDataReceived(device: BluetoothDevice, data: Data) {
                stateManager.notifyDataReceived?.invoke(device, data)
            }

            override fun onDeviceConnecting(device: BluetoothDevice) {
                logD(TAG, "Start BLE connect: ${device.address}")
            }

            @SuppressLint("MissingPermission")
            override fun onDeviceConnected(device: BluetoothDevice) {
                // 更新状态管理器状态
                stateManager.updateState(BleState.CONNECTED)
                stateManager.setConfigurationConnected(
                    configPrefs.containsBleAddress() &&
                    device.address == configPrefs.getBleAddress()
                )

                // 只记录蓝牙连接完成，不结束整个连接会话
                ConnectionAnalytics.endBluetoothConnect()

                logD(TAG, "BLE连接成功: ${device.address}")
                stateManager.notifyDeviceConnected?.invoke(device)
            }

            override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
                // BLE连接失败，但不结束整个连接会话，让上层决定是否重试或结束
                logD(TAG, "BLE连接失败: ${device.address}, reason: $reason")
                stateManager.updateState(BleState.IDLE)

                stateManager.notifyDeviceConnectionFailed?.invoke(device, reason)
            }

            override fun onDeviceReady(device: BluetoothDevice) {
                // 设备就绪，可以开始数据传输
            }

            override fun onDeviceDisconnecting(device: BluetoothDevice) {
                stateManager.updateState(BleState.DISCONNECTING)
            }

            override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
                // 更新状态管理器状态
                stateManager.updateState(BleState.IDLE)
                stateManager.updateDevice(null)
                logD(TAG, "Device disconnected with reason: $reason")
                stateManager.notifyDeviceDisconnected?.invoke(device, reason)
            }

            override fun onWriteRequestFailed(device: BluetoothDevice, status: Int) {
                logD(TAG, "Write request failed with status: $status")
            }

            override fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {
                logE(TAG, "Enable notification failed with status: $status")
                stateManager.notifyNotificationFailed?.invoke(device, status)
            }
        }
    }

    fun write(value: ByteArray) {
        bleManager?.write(value)
    }

    fun release(isManualRelease: Boolean = true) {
        logD(TAG, "Releasing BLE manager, manual: $isManualRelease")
        if (isManualRelease) {
            // 设备清理通过状态管理器处理
            stopScan()
        }
        cancelDelayedScan()
        releaseManager()
    }

    private fun cancelDelayedScan() {
        delayScanJob?.cancel()
        delayScanJob = null
    }

    private fun releaseManager() {
        bleManager?.release()
        bleManager = null
    }

    private fun clearDevices() {
        discoveredBleDevices.clear()
        stateManager.notifyScanResults?.invoke(discoveredBleDevices)
    }

    fun closeConnect() {
        // 更新状态管理器状态
        stateManager.updateState(BleState.IDLE)
        stateManager.setAutoConnectionEnabled(false)

        logD(TAG, "Force closing connection")
        releaseManager()
    }

    fun isConnected(): Boolean {
        return bleManager?.isConnected == true
    }

    fun state(): ConnectionState? {
        return bleManager?.state
    }

    /**
     * 启动扫描（优化版本）
     * @param enableAutoConnection 是否启用自动连接
     */
    fun startScan(enableAutoConnection: Boolean = true) {
        // 使用状态管理器更新自动连接设置
        stateManager.setAutoConnectionEnabled(enableAutoConnection)

        // 检查是否有配置的设备地址
//        if (stateManager.isAutoConnectionEnabled && !hasConfiguredDevices()) {
//            logD(TAG, "No BLE address configured, skipping scan")
//            return
//        }

        // 检查连接状态
        if (isConnected()) {
            logD(TAG, "BLE already connected, returning cached devices")
            stateManager.notifyScanResults?.invoke(discoveredBleDevices)
            return
        }

        // 检查权限和硬件状态
        val permissionCheck = checkPermissionsAndHardware()
        if (permissionCheck != PermissionCheckResult.GRANTED) {
            handlePermissionCheckResult(permissionCheck)
            return
        }

        // 检查扫描频率限制
        val now = System.currentTimeMillis()
        val timeSinceLastScan = now - lastScanTimestamp

        when {
            stateManager.currentState == BleState.SCANNING -> handleAlreadyScanning(now, timeSinceLastScan)
            timeSinceLastScan < SLICE_SCANNING_PERIOD_MS -> handleTooFrequentScan(timeSinceLastScan)
            else -> {
                lastScanTimestamp = now
                startScanInternal()
            }
        }
    }

    /**
     * 检查是否有配置的设备
     */
    private fun hasConfiguredDevices(): Boolean {
        return configPrefs.getBleAddress() != null || configPrefs.getScanBleAddress() != null
    }

    @SuppressLint("MissingPermission")
    private fun connectBestDevice(discoveredBleDevices: List<BleDevice>) {
        val deviceToConnect = deviceSelectionStrategy.select(discoveredBleDevices)
        if (deviceToConnect == null) {
            logW(
                TAG,
                "No suitable device found by strategy chain: ${deviceSelectionStrategy::class.simpleName}"
            )
            return
        }
        stateManager.setConfigurationConnected(
            configPrefs.containsBleAddress() && deviceToConnect.device.address == configPrefs.getBleAddress()
        )
        connect(deviceToConnect.device, SPRiderServices.getSharedInstance().application)
    }

    private val bluetoothStateBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.STATE_OFF)
            val previousState = intent.getIntExtra(
                BluetoothAdapter.EXTRA_PREVIOUS_STATE, BluetoothAdapter.STATE_OFF
            )
            when (state) {
                BluetoothAdapter.STATE_ON -> {
                    logD(TAG, "Bluetooth is on")
                    startScan()
                }

                BluetoothAdapter.STATE_TURNING_OFF, BluetoothAdapter.STATE_OFF -> {
                    logD(TAG, "Bluetooth is off")
                    if (previousState != BluetoothAdapter.STATE_TURNING_OFF && previousState != BluetoothAdapter.STATE_OFF) {
                        stopScan()
                    }
                }
            }
        }
    }

    private val locationProviderChangedReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {

        }
    }

    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            super.onScanResult(callbackType, result)
            if (!ScanFilters.isNoise(result)) {
                flow {
                    deviceDiscovered(result)
                    emit(discoveredBleDevices.filter {
                        ScanFilters.matchesUuidFilter(it.scanResult) && ScanFilters.matchesNearbyFilter(
                            it.rssi
                        )
                    })
                }.collectWithScope(mainScope) { bleDevices ->
                    stateManager.notifyScanResults?.invoke(bleDevices)
                }
            }
        }

        override fun onBatchScanResults(results: MutableList<ScanResult>) {
            super.onBatchScanResults(results)

            if (results.isEmpty()) return
            flow {
                results.filter { !ScanFilters.isNoise(it) }.forEach {
                    deviceDiscovered(it)
                }
                emit(discoveredBleDevices.filter {
                    ScanFilters.matchesManufacturerFilter(it.scanResult) && ScanFilters.matchesNearbyFilter(
                        it.rssi
                    )
                })
            }.collectWithScope(mainScope) { bleDevices ->
                // 更新状态管理器中的发现设备列表
                stateManager.updateDiscoveredDevices(bleDevices)

                bleDevices.forEach {
                    logD(TAG, "Device found: ${it.name} - ${it.device.address}")
                }
                stateManager.notifyScanResults?.invoke(bleDevices)
                if (stateManager.isAutoConnectionEnabled) {
                    connectBestDevice(bleDevices)
                }
            }
        }

        override fun onScanFailed(errorCode: Int) {
            super.onScanFailed(errorCode)
            logW(TAG, "Scanning failed with code $errorCode")
            stateManager.updateState(BleState.ERROR)
            // 扫描失败不单独通知，通过错误状态体现
        }
    }

    private fun startScanInternal() {
        // 更新状态管理器状态
        stateManager.updateState(BleState.SCANNING)

        // 记录扫描开始
        ConnectionAnalytics.startBluetoothScan()

        logD(TAG, "开始BLE扫描")

        // 扫描开始不需要单独通知
        val settings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setReportDelay(TIMEOUT_SCAN)
            .setUseHardwareBatchingIfSupported(false)
            .build()
        val filter = ScanFilter.Builder()
            .setManufacturerData(ScanFilters.manufacturerId(), null)
            .build()
        val filters = listOf(filter)
        val scanner = BluetoothLeScannerCompat.getScanner()
        scanner.startScan(filters, settings, scanCallback)
        clearDevices()
    }

    private fun delayScan(delay: Long) {
        cancelDelayedScan()
        delayScanJob = mainScope.launch(Dispatchers.IO) {
            delay(delay)
            startScan()
        }
    }

    fun stopScan() {
        // 更新状态管理器状态
        if (stateManager.currentState == BleState.SCANNING) {
            stateManager.updateState(BleState.IDLE)
        }

        // 只记录扫描结束，不结束整个连接会话
        ConnectionAnalytics.endBluetoothScan()

        logD(TAG, "结束BLE扫描，发现设备: ${discoveredBleDevices.size} currentState: ${stateManager.currentState}")
        if (stateManager.currentState == BleState.SCANNING) {
            val scanner = BluetoothLeScannerCompat.getScanner()
            scanner.stopScan(scanCallback)
        }
    }

    fun getBleList(): List<BleDevice> {
        return discoveredBleDevices
    }

    @Synchronized
    private fun deviceDiscovered(result: ScanResult) {
        val index = discoveredBleDevices.indexOfFirst { it.device.address == result.device.address }
        if (index == -1) {
            discoveredBleDevices.add(BleDevice(result))
        } else {
            discoveredBleDevices[index] = discoveredBleDevices[index].update(result)
        }
    }

    fun registerBroadcastReceivers(context: Context) {
        context.registerReceiver(
            bluetoothStateBroadcastReceiver,
            IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED)
        )
        context.registerReceiver(
            locationProviderChangedReceiver,
            IntentFilter(LocationManager.MODE_CHANGED_ACTION)
        )
    }

    fun unregisterBroadcastReceivers(context: Context) {
        try {
            context.unregisterReceiver(bluetoothStateBroadcastReceiver)
            context.unregisterReceiver(locationProviderChangedReceiver)
        } catch (ignore: Exception) {
            logW(TAG, "Failed to unregister broadcast receivers", ignore)
        }
    }

    fun getCurrentConnectDevice(): BluetoothDevice? {
        return stateManager.currentDevice
    }

    /**
     * 检查权限和硬件状态
     */
    private fun checkPermissionsAndHardware(): PermissionCheckResult {
        return when {
            !BleUtils.isBleEnabled() -> PermissionCheckResult.BLUETOOTH_DISABLED
            !BleUtils.isLocationPermissionGranted(SPRiderServices.getSharedInstance().application) ->
                PermissionCheckResult.LOCATION_PERMISSION_NEEDED

            BleUtils.isSorAbove() && !BleUtils.isBluetoothScanPermissionGranted(SPRiderServices.getSharedInstance().application) ->
                PermissionCheckResult.BLUETOOTH_SCAN_PERMISSION_NEEDED

            else -> PermissionCheckResult.GRANTED
        }
    }

    /**
     * 处理权限检查结果
     */
    private fun handlePermissionCheckResult(result: PermissionCheckResult) {
        when (result) {
            PermissionCheckResult.BLUETOOTH_DISABLED ->
                stateManager.notifyBluetoothDisabled?.invoke()

            PermissionCheckResult.LOCATION_PERMISSION_NEEDED ->
                stateManager.notifyLocationPermissionRequired?.invoke()

            PermissionCheckResult.BLUETOOTH_SCAN_PERMISSION_NEEDED ->
                stateManager.notifyBluetoothScanPermissionRequired?.invoke()

            PermissionCheckResult.GRANTED -> { /* 已处理 */
            }
        }
    }

    /**
     * 处理已在扫描的情况
     */
    private fun handleAlreadyScanning(now: Long, timeSinceLastScan: Long) {
        logD(TAG, "BLE already scanning")
        if (timeSinceLastScan > SCANNING_DURATION) {
            stopScan()
            lastScanTimestamp = now
            startScanInternal()
        } else {
            // 已经在扫描中，无需额外通知
        }
    }

    /**
     * 处理扫描过于频繁的情况
     */
    private fun handleTooFrequentScan(timeSinceLastScan: Long) {
        logD(TAG, "StartScan too frequent, ${timeSinceLastScan}ms since last scan")
        if (SCHEDULE_START_SCAN_WHEN_STARTED_TOO_FREQUENTLY) {
            val delay = SLICE_SCANNING_PERIOD_MS - timeSinceLastScan
            logD(TAG, "Scheduling delayed scan after ${delay}ms")
            delayScan(delay)
        }
    }

    /**
     * 权限检查结果枚举
     */
    private enum class PermissionCheckResult {
        GRANTED,
        BLUETOOTH_DISABLED,
        LOCATION_PERMISSION_NEEDED,
        BLUETOOTH_SCAN_PERMISSION_NEEDED
    }
}