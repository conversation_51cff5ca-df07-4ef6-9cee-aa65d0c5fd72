package com.link.riderservice.feature.cast.video

import android.content.Context
import android.hardware.display.DisplayManager
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.media.MediaFormat.MIMETYPE_VIDEO_AVC
import android.media.projection.MediaProjection
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.view.Display
import com.link.riderservice.api.SPNaviMode
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.core.logging.logW
import com.link.riderservice.feature.cast.autolink.source.ALVideoSource
import com.link.riderservice.feature.cast.config.VideoPackage
import com.link.riderservice.feature.cast.video.codec.EncoderController
import com.link.riderservice.feature.cast.video.display.VirtualDisplayController
import com.link.riderservice.feature.cast.video.render.RenderLoop

/**
 * 屏幕投送编排控制器：负责编解码器、虚拟显示与渲染循环的编排。
 */
internal class ScreenCastController(
    context: Context,
    private val onDisplayInitialized: (display: Display) -> Unit,
    private val onDisplayReleased: (display: Display) -> Unit,
    private val onRequestMediaProjection: () -> Unit,
    private val onMirrorStart: () -> Unit,
    private val onMirrorStop: () -> Unit,
    private val onPresentationStart: () -> Unit = {},
    private val onPresentationStop: () -> Unit = {},
    private val onEncodedFrame: (pkg: VideoPackage) -> Unit
) : ALVideoSource.IVideoCallback {

    companion object {
        private const val TAG = "ScreenCastController"
    }

    private val displayManager: DisplayManager =
        context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager

    private val codecHandlerThread: HandlerThread = HandlerThread(VideoDefaults.CODEC_THREAD_NAME)
    private val backgroundHandler: Handler

    private val encoderController: EncoderController
    private val virtualDisplayController: VirtualDisplayController

    private var navigationMode: SPNaviMode = SPNaviMode.SPNaviModeDefaultNavi
    private var displayWidth: Int = 0
    private var displayHeight: Int = 0

    private var renderThread: Thread? = null
    private var renderLoop: RenderLoop? = null

    private val videoPackage = VideoPackage()

    init {
        codecHandlerThread.start()
        backgroundHandler = Handler(codecHandlerThread.looper)
        encoderController = EncoderController { backgroundHandler.looper }
        virtualDisplayController =  VirtualDisplayController(
            displayManager,
            backgroundHandler,
            onDisplayInitialized,
            onDisplayReleased,
            onMirrorStart,
            onMirrorStop,
            onPresentationStart,
            onPresentationStop
        )
    }

    fun setNaviMode(newNavigationMode: SPNaviMode) {
        logD(TAG, "setNaviMode $newNavigationMode")
        navigationMode = newNavigationMode
    }

    fun setMediaProjection(newMediaProjection: MediaProjection) {
        virtualDisplayController.attachMediaProjection(newMediaProjection)
        startRenderProcess()
    }

    override fun onVideoConfig(codec: Int, fps: Int, w: Int, h: Int) {
        logD(TAG, "onVideoConfig: ${w}x${h}@${fps}fps")
        updateDisplayDimensions(w, h)
        configureAndPrepareEncoder(fps)

        if (navigationMode == SPNaviMode.SPNaviModeMirrorNavi) {
            if (!virtualDisplayController.hasMediaProjection()) {
                logW(TAG, "MediaProjection is null, requesting projection")
                onRequestMediaProjection()
                return
            }
        }
    }

    override fun onStart() {
        logD(TAG, "onStart $navigationMode")
        encoderController.start()
        startRenderProcess()
    }

    override fun onStop() {
        logD(TAG, "onStop navigationMode=$navigationMode")
        encoderController.stop()
        stopRender()
        virtualDisplayController.release()
        if (navigationMode != SPNaviMode.SPNaviModeLockScreenNavi && navigationMode != SPNaviMode.SPNaviModeMirrorNavi) {
            onPresentationStop()
        } else if (virtualDisplayController.hasMediaProjection()) {
            virtualDisplayController.detachMediaProjection()
        }
    }

    override fun onShutdown() {
        logD(TAG, "onShutdown")
        onStop()
    }

    fun onShutdownPublic() {
        onShutdown()
    }

    private fun updateDisplayDimensions(width: Int, height: Int) {
        displayWidth = width
        displayHeight = height
    }

    private fun configureAndPrepareEncoder(framesPerSecond: Int) {
        val format = MediaFormat().apply {
            setString(MediaFormat.KEY_MIME, MIMETYPE_VIDEO_AVC)
            setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
            setInteger(MediaFormat.KEY_BIT_RATE, VideoDefaults.BITRATE)
            setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, VideoDefaults.I_FRAME_INTERVAL)
            setInteger(MediaFormat.KEY_WIDTH, displayWidth)
            setInteger(MediaFormat.KEY_HEIGHT, displayHeight)
            setInteger(MediaFormat.KEY_FRAME_RATE, framesPerSecond)
            setInteger(MediaFormat.KEY_CAPTURE_RATE, framesPerSecond)
            setInteger(MediaFormat.KEY_REPEAT_PREVIOUS_FRAME_AFTER, 1000000 / framesPerSecond)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileHigh)
                setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel41)
            }
        }
        encoderController.setEncodedFrameListener(object : EncoderController.EncodedFrameListener {
            override fun onEncodedFrame(data: ByteArray, size: Int, flags: Int) {
                videoPackage.data = data
                videoPackage.size = size
                videoPackage.flags = flags
                onEncodedFrame(videoPackage)
            }
        })
        encoderController.configureAndPrepare(format)
    }

    private fun startRenderProcess() {
        if (navigationMode == SPNaviMode.SPNaviModeMirrorNavi && !virtualDisplayController.hasMediaProjection()) {
            logW(TAG, "startRender: mediaProjection is null")
            return
        }
        stopRender()
        startRender()
    }

    private fun startRender() {
        renderLoop = RenderLoop(
            displayWidth = { displayWidth },
            displayHeight = { displayHeight },
            encoderInputSurfaceProvider = { encoderController.getInputSurface() },
            backgroundHandler = backgroundHandler,
            createVirtualDisplay = { srcSurface ->
                val densityDpi = DisplayUtils.calculateDensityDpi(displayWidth, displayHeight)
                if (navigationMode == SPNaviMode.SPNaviModeMirrorNavi) {
                    virtualDisplayController.createMirror(srcSurface, displayWidth, displayHeight, densityDpi)
                } else {
                    virtualDisplayController.createPresentation(srcSurface, displayWidth, displayHeight, densityDpi)
                }
            }
        )
        renderThread = Thread(renderLoop, VideoDefaults.SCREEN_CAPTURE_THREAD_NAME).apply { start() }
    }

    private fun stopRender() {
        try {
            renderLoop?.requestStop()
            renderThread?.join()
        } catch (e: InterruptedException) {
            logE(TAG, "Interrupted while waiting for render thread", e)
        } finally {
            renderThread = null
            renderLoop = null
        }
    }
}


