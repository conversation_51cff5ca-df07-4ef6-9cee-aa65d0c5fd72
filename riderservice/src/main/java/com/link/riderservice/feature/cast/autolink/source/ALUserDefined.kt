package com.link.riderservice.feature.cast.autolink.source


import com.link.riderservice.core.logging.logI
import com.link.riderservice.feature.cast.autolink.project.Protos
import com.link.riderservice.feature.cast.autolink.project.UserDefined
import com.link.riderservice.feature.cast.autolink.project.UserDefinedCallbacks


internal class ALUserDefined(
    private val userDefinedListener: UserDefinedListener? = null
) : ALServiceBase {

    interface UserDefinedListener {
        fun onRawDataRequest(msg: ByteArray)
        fun onRawDataResponse()
    }

    private var userDefined: UserDefined? = null
    fun sendRawDataRequest(msg: ByteArray) {
        userDefined?.nativeRawDataRequest(msg)
    }

    fun sendRawDataResponse() {
        userDefined?.nativeRawDataResponse()
    }

    override fun destroy() {
        userDefined?.destroy()
    }

    override fun create(serviceId: Int, nativeGalReceiver: Long): Boolean {
        return userDefined?.create(serviceId, nativeGalReceiver) ?: false
    }

    override fun start(): Boolean {
        return true
    }

    override val serviceType: Int
        get() = ALServiceBase.AL_SERVICE_UNKONWN // UserDefined服务类型
    override val nativeInstance: Long
        get() = userDefined?.nativeInstance ?: 0

    companion object {
        private const val TAG = "ALUserDefined"
    }

    init {
        val userDefinedCallbacks: UserDefinedCallbacks = object : UserDefinedCallbacks {
            override fun onChannelOpened(): Int {
                logI(TAG, "UserDefined channel opened")
                return Protos.STATUS_SUCCESS
            }

            override fun onRawDataRequest(msg: ByteArray) {
                logI(TAG, "Received raw data request, size: ${msg.size}")
                userDefinedListener?.onRawDataRequest(msg)
                sendRawDataResponse()
            }

            override fun onRawDataResponse() {
                logI(TAG, "Received raw data response")
                userDefinedListener?.onRawDataResponse()
            }
        }
        userDefined = UserDefined(userDefinedCallbacks)
    }
}