package com.link.riderservice.feature.cast.autolink

import android.os.Build
import com.link.riderservice.core.utils.Platform.getDisplayRotation
import com.link.riderservice.feature.cast.autolink.project.GalReceiver
import com.link.riderservice.feature.cast.autolink.project.PhoneInfo
import com.link.riderservice.feature.cast.autolink.source.ALServiceBase
import com.link.riderservice.feature.cast.autolink.source.ALUserDefined
import com.link.riderservice.feature.cast.autolink.source.ALVideoSource
import com.link.riderservice.feature.cast.config.AlConfig
import com.link.riderservice.feature.cast.config.VideoPackage
import com.link.riderservice.feature.connection.transport.Transport

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class ALIntegration {
    companion object {
        init {
            /**
             * 加载 libaljni.so
             */
            System.loadLibrary("aljni")
        }
    }

    private var galReceiver: GalReceiver? = null
    private var videoSource: ALVideoSource? = null
    private var userDefined: ALUserDefined? = null
    private var isExited = true

    /**
     * 初始化
     * @param config 配置
     * @param videoListener 视频监听
     * @param appMessageListener 应用消息监听
     * @param byeByeHandler 再见处理
     * @param autoStart 是否自动启动 默认 false
     */
    fun init(
        config: AlConfig,
        videoListener: ALVideoSource.IVideoCallback,
        userDefinedListener: ALUserDefined.UserDefinedListener,
        appMessageListener: GalReceiver.AppMessageListener,
        byeByeHandler: GalReceiver.ByeByeHandler,
        autoStart: Boolean = false
    ) {
        videoSource = ALVideoSource(
            config.videoCfg, videoListener, appMessageListener, autoStart,
            config.remoteHost
        )
        userDefined = ALUserDefined(userDefinedListener)
        val phoneInfo = PhoneInfo(
            Build.MANUFACTURER + config.appName, Build.MODEL,
            config.version, config.phoneName, config.screenWidth,
            config.screenHeight
        )
        galReceiver = GalReceiver(phoneInfo, appMessageListener, byeByeHandler)
    }

    /**
     * 开启 GAL
     */
    fun start(transport: Transport?) {
        galReceiver?.startTransport(transport)
    }

    /**
     * 销毁 GAL
     */
    fun destroy() {
        galReceiver?.destroy()
        galReceiver = null
        videoSource = null
        userDefined = null
    }

    /**
     * 发送手机屏幕信息
     * @param isLandscape 是否横屏
     * @param rotation 旋转角度
     */
    @Synchronized
    fun sendOrientation(isLandscape: Int, rotation: Int) {
        if (!isExited) {
            galReceiver?.nativeSendScreenOrientationNotifi(
                isLandscape,
                getDisplayRotation(rotation)
            )
        }

    }

    /**
     * 发送分辨率通知
     * @param width 屏幕宽度
     * @param height 屏幕高度
     * @param isRequired 是否是平台主动请求
     */
    @Synchronized
    fun sendResolutionNotification(width: Int, height: Int, isRequired: Boolean) {
        if (!isExited) {
            galReceiver?.nativeSendScreenResolutionNotification(
                width,
                height,
                isRequired
            )
        }
    }

    /**
     * 发送当前的 focus 给平台，该函数为了控制平台的焦点
     * @param focus [Protos.VIDEO_FOCUS_PROJECTED] or [Protos.VIDEO_FOCUS_NATIVE]
     * @param reason 原因
     * @see Protos
     */
    @Synchronized
    fun sendEncoderState(focus: Int, reason: Int) {
        if (!isExited) {
            videoSource?.sendVideoFocusRequestNotify(0, focus, reason)
        }
    }

    /**
     * 发送视频帧给平台
     * @param videoPackage 视频数据
     * @see VideoPackage
     */
    @Synchronized
    fun sendFrame(videoPackage: VideoPackage) {
        if (!isExited) {
            videoSource?.sendData(videoPackage)
        }
    }

    /**
     * 发送再见请求给平台
     */
    fun sendByeByeRequest() {
        galReceiver?.sendByeByeRequest(1)
    }

    private fun startAllChanel() {
        if (galReceiver != null) {
            if (videoSource != null) {
                galReceiver?.registerCarService(
                    ALServiceBase.AL_SERVICE_VIDEO_SOURCE,
                    videoSource!!
                )
            }
            if (userDefined != null) {
                galReceiver?.registerCarService(
                    ALServiceBase.AL_SERVICE_USER_DEFINED,
                    userDefined!!
                )
            }
            galReceiver?.start()
        }
    }

    /**
     * 退出
     */
    @Synchronized
    fun exit() {
        videoSource?.exitVideoSession()
        galReceiver?.destroyCarServices()
        galReceiver?.sendExitResponse()
        isExited = true
    }

    /**
     * 启动所有的通道
     */
    @Synchronized
    fun startAllChannel() {
        startAllChanel()
        isExited = false
    }

    fun requestLockScreenDisplay() {
        videoSource?.sendDisplayAreaChangeResponse()
    }

    fun sendRawDataRequest(msg: ByteArray) {
        if (!isExited) {
            userDefined?.sendRawDataRequest(msg)
        }
    }

    fun sendRawDataResponse() {
        if (!isExited) {
            userDefined?.sendRawDataResponse()
        }
    }
}