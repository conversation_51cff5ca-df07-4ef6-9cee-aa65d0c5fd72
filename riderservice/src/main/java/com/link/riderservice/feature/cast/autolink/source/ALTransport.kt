package com.link.riderservice.feature.cast.autolink.source

import android.os.Process
import android.os.Trace
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.feature.connection.transport.Transport
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Input/output processing for GAL.
 */
internal class ALTransport(private val frameParser: FrameParser) {
    interface FrameParser {
        fun getFrameSizeToRead(headerBytes: ByteArray, frameSize: Int): Int
        fun enqueueIncomingFrame(frameBytes: ByteArray, frameSize: Int)
        fun dequeueOutgoingFrame(outputBuffer: ByteArray): Int
        fun readerTerminated()
        fun onIoError()
    }

    private val stopRequested = AtomicBoolean(false)
    private var transport: Transport? = null

    /**
     * 开启数据交互
     * @param newTransport
     * @see Transport
     */
    fun start(newTransport: Transport?) {
        <EMAIL> = newTransport
        stopRequested.set(false)
        mReaderThread.start()
        mWriterThread.start()
    }

    /**
     * 关闭数据交互
     */
    fun stop() {
        if (requestStop()) {
            transport?.stopTransport()
        }
    }

    private val isStopRequested: Boolean
        get() = !stopRequested.get()

    private fun requestStop(): Boolean {
        return !stopRequested.getAndSet(true)
    }

    private fun hexdumpBuffer(frameBuffer: ByteArray, bytesToWrite: Int): String {
        val hexString = StringBuilder()
        for (i in 0 until bytesToWrite) {
            hexString.append(String.format(" %02x", frameBuffer[i]))
            if (i == 32) {
                hexString.append("...")
                break
            }
        }
        return hexString.toString()
    }

    private val mReaderThread: Thread = object : Thread("ReaderThread") {
        private fun read(frameBuffer: ByteArray, bufferOffset: Int, bytesToWrite: Int): Int {
            if (DBG_TRACE) {
                Trace.beginSection("read")
            }
            var bytesRead = 0
            while (bytesRead < bytesToWrite && isStopRequested) {
                bytesRead += try {
                    val nbytes = transport?.read(frameBuffer, bufferOffset + bytesRead, bytesToWrite - bytesRead) ?: 0
                    if (nbytes > 0) {
                        nbytes
                    } else {
                        throw IOException("the peer has performed an orderly shutdown")
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                    logE(TAG, "Caught exception on read. Exiting")
                    <EMAIL>()
                    frameParser.onIoError()
                    break
                }
            }
            if (DBG_TRACE) {
                Trace.endSection()
            }
            return bytesRead
        }

        val HEADER_READ_LENGTH = 4
        override fun run() {
            Process.setThreadPriority(READER_THREAD_PRIORITY)
            val frameBuffer = ByteArray(MAX_FRAME_SIZE)
            while (isStopRequested) {
                if (DBG_TRACE) {
                    Trace.beginSection("readerThread")
                }
                var bytesRead = read(frameBuffer, 0, HEADER_READ_LENGTH)
                if (bytesRead != HEADER_READ_LENGTH) {
                    logE(TAG, "read returned $bytesRead while expecting $HEADER_READ_LENGTH")
                    break
                }
                val bytesToWrite = frameParser.getFrameSizeToRead(frameBuffer, HEADER_READ_LENGTH)
                bytesRead = read(frameBuffer, HEADER_READ_LENGTH, bytesToWrite)
                if (bytesRead != bytesToWrite) {
                    logE(TAG, "read returned $bytesRead while expecting $bytesToWrite")
                    break
                }
                if (VERBOSE_LOGGING_ENABLED) {
                    logD(TAG, "Got frame:" + hexdumpBuffer(frameBuffer, bytesToWrite + HEADER_READ_LENGTH))
                }
                if (isStopRequested) {
                    frameParser.enqueueIncomingFrame(frameBuffer, bytesToWrite + HEADER_READ_LENGTH)
                }
                if (DBG_TRACE) {
                    Trace.endSection()
                }
            }
            frameParser.readerTerminated()
        }
    }
    private val mWriterThread: Thread = object : Thread("WriterThread") {
        override fun run() {
            Process.setThreadPriority(WRITER_THREAD_PRIORITY)
            val frameBuffer = ByteArray(MAX_FRAME_SIZE)
            try {
                while (isStopRequested) {
                    if (DBG_TRACE) {
                        Trace.beginSection("writerThread")
                    }
                    val bytesToWrite = frameParser.dequeueOutgoingFrame(frameBuffer)
                    if (bytesToWrite == 0) {
                        logE(TAG, "Writer thread shutting down")
                        break
                    }
                    if (VERBOSE_LOGGING_ENABLED) {
                        logD(TAG, "Sending frame:" + hexdumpBuffer(frameBuffer, bytesToWrite))
                    }
                    if (DBG_TRACE) {
                        Trace.beginSection("write")
                    }
                    val retryMax = 1
                    var retryCount = 0
                    while (retryCount < retryMax && isStopRequested) {
                        try {
                            transport?.write(frameBuffer, 0, bytesToWrite)
                            break
                        } catch (e: IOException) {
                            e.printStackTrace()
                            retryCount++
                        }
                    }
                    if (retryCount >= retryMax) {
                        throw IOException("max retry reached")
                    }
                    if (DBG_TRACE) {
                        // for both writerThread and write
                        Trace.endSection()
                        Trace.endSection()
                    }
                }
            } catch (e: IOException) {
                logE(TAG, "Caught exception on write. Exiting", e)
                <EMAIL>()
                frameParser.onIoError()
            }
        }
    }

    companion object {
        private const val TAG = "InputOutputThreads"
        private const val DBG_TRACE = false
        private const val VERBOSE_LOGGING_ENABLED = false
        private const val MAX_FRAME_SIZE = 4 + 4 + 65536
        private const val READER_THREAD_PRIORITY = Process.THREAD_PRIORITY_URGENT_AUDIO
        private const val WRITER_THREAD_PRIORITY = Process.THREAD_PRIORITY_URGENT_AUDIO
    }
}