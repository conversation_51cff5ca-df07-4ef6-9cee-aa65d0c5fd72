package com.link.riderservice.feature.cast.autolink.source

import com.link.riderservice.feature.cast.autolink.project.NativeObject

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal interface ALServiceBase : NativeObject {
    fun create(serviceId: Int, nativeGalReceiver: Long): Boolean
    fun start(): Boolean
    val serviceType: Int

    companion object {
        const val AL_SERVICE_UNKONWN = 0
        const val AL_SERVICE_ROOT = 1
        const val AL_SERVICE_ALL = 2
        const val AL_SERVICE_CONTROL = 3
        const val AL_SERVICE_VIDEO_SOURCE = 4
        const val AL_SERVICE_BLUETOOTH = 5
        const val AL_SERVICE_USER_DEFINED = 6
    }
}