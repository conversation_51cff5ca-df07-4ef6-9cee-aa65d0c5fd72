package com.link.riderservice.feature.connection.ble.core.strategy

import com.link.riderservice.api.SPRiderServices
import com.link.riderservice.data.local.ConfigPreferences
import com.link.riderservice.feature.connection.ble.model.BleDevice

/**
 * 手动扫描地址优先策略
 * - 命中后会移除一次性地址（scanBleAddress）
 */
internal class ManualPreferredStrategy : DeviceSelectionStrategy {
    override fun select(devices: List<BleDevice>): BleDevice? {
        val context = SPRiderServices.getSharedInstance().application
        val prefs = ConfigPreferences.getInstance(context)
        if (!prefs.containsScanBleAddress()) return null

        val address = prefs.getScanBleAddress()
        val device = devices.find { it.device.address == address }
        // 命中后清理一次性配置
        prefs.removeScanBleAddress()
        return device
    }
}






