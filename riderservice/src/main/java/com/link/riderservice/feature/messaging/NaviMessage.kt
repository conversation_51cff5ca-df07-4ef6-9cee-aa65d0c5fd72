package com.link.riderservice.feature.messaging

import com.link.riderservice.core.extensions.writeInt16BE
import com.link.riderservice.core.extensions.writeInt32BE


internal data class NaviMessage(
    val header: Int = 0,
    val frameLength: Int = 0,
    val messageLength: Int = 0,
    val messagePayload: ByteArray? = null,
    val originalMessageData: ByteArray? = null
) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as NaviMessage

        if (header != other.header) return false
        if (frameLength != other.frameLength) return false
        if (messageLength != other.messageLength) return false
        if (messagePayload != null) {
            if (other.messagePayload == null) return false
            if (!messagePayload.contentEquals(other.messagePayload)) return false
        } else if (other.messagePayload != null) return false
        if (originalMessageData != null) {
            if (other.originalMessageData == null) return false
            if (!originalMessageData.contentEquals(other.originalMessageData)) return false
        } else if (other.originalMessageData != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = header
        result = 31 * result + frameLength
        result = 31 * result + messageLength
        result = 31 * result + (messagePayload?.contentHashCode() ?: 0)
        result = 31 * result + (originalMessageData?.contentHashCode() ?: 0)
        return result
    }
}

/**
 * header:1byte
 * frameLength:2byte
 * messageLength:0byte or 4 byte
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * +  header  +  frameLength  +  messageLength  +  messagePayload   +
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 */
internal fun NaviMessage.toByteArray(): ByteArray {
    var offset = 0

    /** 初始化数据包长度, header + frameLength */
    var len = 3
    if (messageLength != 0) {
        /** 加上 messageLength */
        len += 4
    }
    len += frameLength
    val bytes = ByteArray(len)
    bytes[offset] = (header and 0xff).toByte()
    offset += 1
    bytes.writeInt16BE(frameLength, offset)
    offset += 2
    if (messageLength != 0) {
        bytes.writeInt32BE(messageLength.toLong(), offset)
        offset += 4
    }
    messagePayload?.let {
        System.arraycopy(it, 0, bytes, offset, frameLength)
    }
    return bytes
}