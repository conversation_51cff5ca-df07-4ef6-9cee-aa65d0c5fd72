package com.link.riderservice.feature.connection.ble.core.strategy

import com.link.riderservice.feature.connection.ble.model.BleDevice

/**
 * 组合策略：按顺序依次尝试各策略，返回第一个非空结果
 */
internal class CompositeStrategy(
    private vararg val strategies: DeviceSelectionStrategy
) : DeviceSelectionStrategy {
    override fun select(devices: List<BleDevice>): BleDevice? {
        for (strategy in strategies) {
            strategy.select(devices)?.let { return it }
        }
        return null
    }
}



