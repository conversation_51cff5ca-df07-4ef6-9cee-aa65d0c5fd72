package com.link.riderservice.feature.connection.wifi.callback

/**
 * WiFi客户端管理器事件监听接口
 */
internal interface IWiFiAPListener {
    /**
     * 开始连接WiFi
     * @param ssid 目标网络SSID
     */
    fun onWifiConnecting(ssid: String)

    /**
     * WiFi连接成功
     * @param ssid 连接的网络SSID
     * @param ipAddress 获得的IP地址
     */
    fun onWifiConnected(ssid: String, ipAddress: String)

    /**
     * WiFi连接断开
     */
    fun onWifiDisconnected()

    /**
     * WiFi连接失败
     * @param reason 失败原因码
     */
    fun onWifiConnectionFailed(reason: Int)

    /**
     * 请求WiFi信息
     */
    fun requestWifiInfo()

    /**
     * WiFi状态变化
     * @param opened WiFi是否开启
     */
    fun onWifiState(opened: Boolean)
} 