package com.link.riderservice.feature.connection.auth

import com.link.riderservice.api.exception.AuthException
import com.link.riderservice.api.exception.NetworkException
import com.link.riderservice.api.exception.SPRiderServicesException
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.core.logging.logW
import com.link.riderservice.data.remote.authorize.AuthorizeRemoteSourceImpl
import com.link.riderservice.data.repository.AuthorizeRepositoryImpl
import com.link.riderservice.domain.model.Authorization
import com.link.riderservice.domain.model.CheckInfo
import com.link.riderservice.feature.connection.auth.AuthenticationManager.authorizeRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.retry

/**
 * 认证管理器
 * 
 * 负责处理设备认证和激活相关的网络请求
 * 包括重试机制、错误处理和消息发送
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-05
 */
internal object AuthenticationManager {
    private const val TAG = "AuthenticationManager"
    private const val SUCCESS = 0
    private const val RETRY_COUNT = 3L
    private const val RETRY_DELAY_MS = 1000L

    private val authorizeRepository = AuthorizeRepositoryImpl(AuthorizeRemoteSourceImpl())

    // 认证错误类型枚举
    private enum class AuthErrorType {
        NETWORK_ERROR,
        SERVER_ERROR
    }

    /**
     * 认证结果回调接口
     */
    interface AuthCallback<T> {
        fun onSuccess(result: T)
        fun onFailure(exception: SPRiderServicesException)
    }

    /**
     * 请求服务器验证
     * @param productKey 产品 Key
     * @param address 平台的MAC地址
     * @param uuid 产品唯一ID
     * @param timestamp 时间戳
     * @param licenseSign 证书签名
     * @param sign 平台签名
     * @param authorizeRepository 认证仓库
     * @param callback 认证结果回调
     */
    suspend fun requestVerify(
        productKey: String,
        address: String,
        uuid: String,
        timestamp: String,
        licenseSign: String,
        sign: String,
        callback: AuthCallback<CheckInfo>
    ) {
        executeAuthRequest(
            requestType = "Verify",
            request = {
                authorizeRepository.requestCheckStatus(
                    productKey, address, uuid, timestamp, licenseSign, sign
                )
            },
            getStatus = { it.status },
            onSuccess = { checkInfo ->
                callback.onSuccess(checkInfo)
            },
            onFailure = { errorType ->
                val exception = when (errorType) {
                    AuthErrorType.NETWORK_ERROR -> NetworkException.NetworkError()
                    AuthErrorType.SERVER_ERROR -> AuthException.InvalidCredentials()
                }
                callback.onFailure(exception)
            }
        )
    }

    /**
     * 请求服务器激活
     * @param sdkKey 平台 SDK 的 key
     * @param macAddr 平台的MAC地址
     * @param timestamp 时间戳
     * @param sign 平台签名
     * @param authorizeRepository 认证仓库
     * @param callback 认证结果回调
     */
    suspend fun requestActivate(
        sdkKey: String,
        macAddr: String,
        timestamp: String,
        sign: String,
        callback: AuthCallback<Authorization>
    ) {
        executeAuthRequest(
            requestType = "Activate",
            request = {
                authorizeRepository.requestActivateStatus(sdkKey, macAddr, timestamp, sign)
            },
            getStatus = { it.status },
            onSuccess = { authorization ->
                callback.onSuccess(authorization)
            },
            onFailure = { errorType ->
                val exception = when (errorType) {
                    AuthErrorType.NETWORK_ERROR -> AuthException.ActivationFailed("网络错误，激活失败")
                    AuthErrorType.SERVER_ERROR -> AuthException.ActivationFailed("服务器激活失败")
                }
                callback.onFailure(exception)
            }
        )
    }

    /**
     * 通用的认证请求处理方法
     * @param T 认证结果类型
     * @param requestType 请求类型（用于日志）
     * @param request 具体的网络请求操作
     * @param getStatus 从结果中获取状态码的方法
     * @param onSuccess 成功回调
     * @param onFailure 失败回调
     */
    private suspend fun <T> executeAuthRequest(
        requestType: String,
        request: suspend () -> Result<T>,
        getStatus: (T) -> Int,
        onSuccess: (T) -> Unit,
        onFailure: (AuthErrorType) -> Unit
    ) {
        flow {
            emit(request())
        }.retry(RETRY_COUNT) { exception ->
            logW(TAG, "$requestType request failed, retrying...", exception)
            delay(RETRY_DELAY_MS)
            exception is Exception
        }.catch { exception ->
            logE(TAG, "$requestType request failed after retries", exception)
            onFailure(AuthErrorType.NETWORK_ERROR)
        }.flowOn(Dispatchers.IO).collect { result ->
            try {
                val authResult = result.getOrThrow()
                val status = getStatus(authResult)
                if (status == SUCCESS) {
                    logD(TAG, "$requestType request succeeded")
                    onSuccess(authResult)
                } else {
                    logW(TAG, "$requestType request failed with status: $status")
                    onFailure(AuthErrorType.SERVER_ERROR)
                }
            } catch (e: Exception) {
                logE(TAG, "$requestType request result processing failed", e)
                onFailure(AuthErrorType.SERVER_ERROR)
            }
        }
    }
}
