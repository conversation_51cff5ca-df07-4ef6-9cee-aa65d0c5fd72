package com.link.riderservice.feature.cast.video.render

import android.graphics.SurfaceTexture
import android.opengl.GLES20
import android.os.Handler
import android.view.Surface
import com.link.riderservice.core.logging.logE
import com.link.riderservice.feature.cast.video.VideoDefaults
import com.link.riderservice.libs.glutils.EglTask
import com.link.riderservice.libs.glutils.FullFrameRect
import com.link.riderservice.libs.glutils.Texture2dProgram
import com.link.riderservice.libs.glutils.WindowSurface

/**
 * 纯渲染循环，负责将 SurfaceTexture 的帧绘制到编码器输入 Surface。
 */
internal class RenderLoop(
    private val displayWidth: () -> Int,
    private val displayHeight: () -> Int,
    private val encoderInputSurfaceProvider: () -> Surface?,
    private val backgroundHandler: Handler,
    private val createVirtualDisplay: (sourceSurface: Surface) -> Unit
) : EglTask(null, 0) {

    private var frameIntervalMillis: Long = VideoDefaults.FRAME_INTERVAL_MILLIS
    private var textureId = 0
    private var sourceSurfaceTexture: SurfaceTexture? = null
    private var sourceSurface: Surface? = null
    private var encoderWindowSurface: WindowSurface? = null
    private var fullFrameRectDrawer: FullFrameRect? = null
    private val textureTransformMatrix = FloatArray(16)
    private var lastFrameTimestampMillis = System.currentTimeMillis()

    @Volatile private var isRecording = false
    @Volatile private var isDrawRequested = false
    private val synchronizationLock = Object()

    override fun onStart() {
        try {
            fullFrameRectDrawer = FullFrameRect(Texture2dProgram(Texture2dProgram.ProgramType.TEXTURE_EXT))
            textureId = fullFrameRectDrawer!!.createTextureObject()
            encoderWindowSurface = WindowSurface(eglCore, encoderInputSurfaceProvider())

            sourceSurfaceTexture = SurfaceTexture(textureId, false).apply {
                setDefaultBufferSize(displayWidth(), displayHeight())
                setOnFrameAvailableListener({
                    if (isRecording) {
                        synchronized(synchronizationLock) {
                            isDrawRequested = true
                            synchronizationLock.notifyAll()
                        }
                    }
                }, backgroundHandler)
            }
            sourceSurface = Surface(sourceSurfaceTexture)
            createVirtualDisplay(sourceSurface!!)
            queueEvent(drawTask)
            isRecording = true
        } catch (e: Exception) {
            logE(TAG, "RenderLoop start error", e)
            releaseSelf()
        }
    }

    override fun onStop() {
        try {
            fullFrameRectDrawer?.release()
            sourceSurface?.release()
            sourceSurfaceTexture?.release()
            encoderWindowSurface?.release()
            makeCurrent()
        } catch (e: Exception) {
            logE(TAG, "RenderLoop stop error", e)
        } finally {
            fullFrameRectDrawer = null
            sourceSurface = null
            sourceSurfaceTexture = null
            encoderWindowSurface = null
        }
    }

    override fun onError(exception: Exception?): Boolean {
        logE(TAG, "RenderLoop error: $exception")
        return true
    }

    override fun processRequest(requestCode: Int, argument1: Int, argument2: Any?): Boolean = false

    private val drawTask: Runnable = object : Runnable {
        override fun run() {
            val shouldDraw = waitForDrawRequest()
            if (!isRecording) {
                releaseSelf()
                return
            }
            if (shouldDraw) {
                sourceSurfaceTexture?.apply {
                    updateTexImage()
                    getTransformMatrix(textureTransformMatrix)
                }
            }
            encodeIfNeeded(shouldDraw)
            clearGLIfNeeded(shouldDraw)
            queueEvent(this)
        }

        private fun waitForDrawRequest(): Boolean {
            var shouldDraw = isDrawRequested
            isDrawRequested = false
            if (!shouldDraw) {
                try {
                    synchronized(synchronizationLock) {
                        synchronizationLock.wait(frameIntervalMillis)
                        shouldDraw = isDrawRequested
                        isDrawRequested = false
                    }
                } catch (_: InterruptedException) {
                    releaseSelf()
                    return false
                }
            }
            return shouldDraw
        }

        private fun encodeIfNeeded(shouldDraw: Boolean) {
            val now = System.currentTimeMillis()
            if (now - lastFrameTimestampMillis > frameIntervalMillis) {
                lastFrameTimestampMillis = now
                if (shouldDraw) {
                    encoderWindowSurface?.apply {
                        makeCurrent()
                        fullFrameRectDrawer?.drawFrame(textureId, textureTransformMatrix)
                        swapBuffers()
                    }
                }
            }
        }

        private fun clearGLIfNeeded(shouldDraw: Boolean) {
            makeCurrent()
            if (shouldDraw) {
                GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT)
                GLES20.glFlush()
            }
        }
    }

    fun requestStop() {
        synchronized(synchronizationLock) {
            isRecording = false
            synchronizationLock.notifyAll()
        }
    }

    companion object {
        private const val TAG = "RenderLoop"
    }
}






