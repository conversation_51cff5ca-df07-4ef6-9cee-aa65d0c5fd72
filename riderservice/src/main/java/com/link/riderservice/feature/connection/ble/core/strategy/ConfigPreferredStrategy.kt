package com.link.riderservice.feature.connection.ble.core.strategy

import com.link.riderservice.api.SPRiderServices
import com.link.riderservice.data.local.ConfigPreferences
import com.link.riderservice.feature.connection.ble.model.BleDevice

/**
 * 配置优先的设备选择策略
 * - 若存在手动扫描指定地址（scanBleAddress），优先匹配并清理该配置
 * - 否则使用已配置的固定地址（bleAddress）
 * - 否则返回 null
 */
internal class ConfigPreferredStrategy : DeviceSelectionStrategy {
    override fun select(devices: List<BleDevice>): BleDevice? {
        val context = SPRiderServices.getSharedInstance().application
        val prefs = ConfigPreferences.getInstance(context)

        // 手动扫描地址优先
        if (prefs.containsScanBleAddress()) {
            val address = prefs.getScanBleAddress()
            val device = devices.find { it.device.address == address }
            // 使用一次后清理
            prefs.removeScanBleAddress()
            return device
        }

        // 其次固定配置地址
        if (prefs.containsBleAddress()) {
            val address = prefs.getBleAddress()
            return devices.find { it.device.address == address }
        }

        return null
    }
}


