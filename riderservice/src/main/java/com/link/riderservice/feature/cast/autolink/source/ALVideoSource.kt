package com.link.riderservice.feature.cast.autolink.source

import com.link.riderservice.core.logging.logD
import com.link.riderservice.feature.cast.autolink.project.GalReceiver
import com.link.riderservice.feature.cast.autolink.project.Protos
import com.link.riderservice.feature.cast.autolink.project.VideoSource
import com.link.riderservice.feature.cast.autolink.project.VideoSourceCallbacks
import com.link.riderservice.feature.cast.config.VideoConfig
import com.link.riderservice.feature.cast.config.VideoPackage

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class ALVideoSource(
    private val videoConfig: VideoConfig?,
    private val videoCallback: IVideoCallback,
    private val appMessageListener: GalReceiver.AppMessageListener,
    autoStart: Boolean,
    remoteHost: String?
) : ALServiceBase {

    /**
     * video source 的回调
     */
    interface IVideoCallback {
        /**
         * 视频配置
         * @param codec 视频格式
         * @param fps 帧率
         * @param w 宽
         * @param h 高
         */
        fun onVideoConfig(codec: Int, fps: Int, w: Int, h: Int)

        /**
         * 开始投屏
         */
        fun onStart()

        /**
         * 停止投屏
         */
        fun onStop()

        /**
         * 销毁投屏
         */
        fun onShutdown()
    }

    private var videoSource: VideoSource? = null
    private var currentSessionId: Int
    private val remoteVideoConfigurations = ArrayList<VideoRemoteConfig>()
    private var currentConfigurationIndex: Int
    private var isVideoSessionOpen = false
    private var currentTimestamp: Long = 0
    private val currentRemoteVideoConfiguration = VideoRemoteConfig()
    private var isLandscapeMode = false
    private var videoFocusState = Protos.VIDEO_FOCUS_NATIVE
    private var permissionStatus = PERMISSION_NO
    private var isVideo :Boolean = false

    override fun destroy() {
        isVideoSessionOpen = false
        videoSource?.destroy()
    }

    override fun create(serviceId: Int, nativeGalReceiver: Long): Boolean {
        return videoSource?.create(serviceId, nativeGalReceiver) ?: false
    }

    override fun start(): Boolean {
        currentTimestamp = System.currentTimeMillis()
        return true
    }

    override val serviceType: Int
        get() = ALServiceBase.AL_SERVICE_VIDEO_SOURCE
    override val nativeInstance: Long
        get() = videoSource?.nativeInstance ?: 0

    fun sendData(pkg: VideoPackage) {
        videoSource?.sendData(System.currentTimeMillis(), pkg.data, pkg.size, pkg.flags)
    }


    fun stop() {
        stopVideoSession()
    }

    fun sendVideoFocusRequestNotify(dispChannel: Int, focus: Int, reason: Int) {
        videoSource?.sendVideoFocusRequestNotifi(dispChannel, focus, reason)
    }

    private fun startVideoSession(isLandscape: Boolean) {
        if (isVideoSessionOpen) {
            stopVideoSession()
        }
        val session = ++currentSessionId
        val width = currentRemoteVideoConfiguration.width
        val height = currentRemoteVideoConfiguration.height
        if (width > height && !isLandscape || width < height && isLandscape) {
            currentRemoteVideoConfiguration.width = height
            currentRemoteVideoConfiguration.height = width
        }

        logD(TAG, "startVideoSession: $currentRemoteVideoConfiguration")
        videoCallback.onVideoConfig(
            currentRemoteVideoConfiguration.codec,
            currentRemoteVideoConfiguration.frameRate,
            currentRemoteVideoConfiguration.width,
            currentRemoteVideoConfiguration.height
        )
        videoSource?.sendStart(
            currentConfigurationIndex,
            session,
            currentRemoteVideoConfiguration.width,
            currentRemoteVideoConfiguration.height
        )
        if (!isVideoSessionOpen) {
            videoCallback.onStart()
        }
        isVideoSessionOpen = true
    }

    private fun stopVideoSession() {
        logD(TAG, "stopVideoSession")
        isVideoSessionOpen = false
        currentSessionId = -1
        videoCallback.onStop()
        //videoCallback.onShutdown()
        videoSource?.sendStop()
    }

    fun exitVideoSession() {
        isVideoSessionOpen = false
        currentSessionId = -1
        videoCallback.onStop()
        videoCallback.onShutdown()
    }

    fun sendDisplayAreaChangeResponse(){
        videoSource?.sendDisplayAreaChangeResponse()
    }

    private fun isSupportConfig(config: VideoRemoteConfig): Boolean {
        return if (config.codec == Protos.MEDIA_CODEC_VIDEO_H264_BP && videoConfig!!.isSupportH264) {
            true
        } else config.codec != Protos.MEDIA_CODEC_VIDEO_MPEG4_ES || !videoConfig!!.isSupportMpeg4
    }

    companion object {
        private const val TAG = "ALVideoSource"
        private const val PERMISSION_UNKNOWN = 0
        private const val PERMISSION_YES = 1
        private const val PERMISSION_NO = 2
    }

    init {
        currentSessionId = -1
        currentConfigurationIndex = -1
        val listener: VideoSourceCallbacks = object : VideoSourceCallbacks {
            override fun videoFocusNotifCallback(focus: Int, unsolicited: Boolean): Int {
                logD(TAG, "videoFocus: $focus--$currentConfigurationIndex")
                if (videoFocusState == focus){
                    logD(TAG, "the same focus")
                    return Protos.STATUS_SUCCESS
                }
                videoFocusState = focus
                if (focus == Protos.VIDEO_FOCUS_PROJECTED) {
                    if (currentConfigurationIndex < 0) {
                        videoSource?.sendVideoFocusRequestNotifi(
                            0, Protos.VIDEO_FOCUS_NATIVE,
                            Protos.NO_VALID_VIDEO_ENCODER
                        )
                    } else if (permissionStatus == PERMISSION_UNKNOWN) {
                        videoSource?.sendVideoFocusRequestNotifi(
                            0, Protos.VIDEO_FOCUS_NATIVE,
                            Protos.WAIT_PERMISSION
                        )
                    } else if (permissionStatus == PERMISSION_NO) {
                        videoSource?.sendVideoFocusRequestNotifi(
                            0, Protos.VIDEO_FOCUS_NATIVE,
                            Protos.NO_PERMISSION
                        )
                    } else {
                        startVideoSession(isLandscapeMode)
                    }
                } else {
                    if (isVideoSessionOpen) {
                        stopVideoSession()
                    }
                }
                return Protos.STATUS_SUCCESS
            }

            override fun displayChangeCallback(
                width: Int, height: Int, isLandscape: Boolean,
                density: Int
            ): Int {
                logD(TAG, "displayChangeCallback: $width--$height--$isLandscape--$density")
                if (isVideo) {//连接成功时必定创建视屏流
                    isVideo = false
                }
//                else{
//                    val needChangeDisplay = RiderService.instance.needChangeDisplay()
//                    if(!needChangeDisplay&&currentRemoteVideoConfiguration.width == width && currentRemoteVideoConfiguration.height == height && isLandscapeMode == isLandscape){
//                        return Protos.STATUS_SUCCESS//在某些情景下不创建视屏流
//                    }
//                }
                currentRemoteVideoConfiguration.width = width
                currentRemoteVideoConfiguration.height = height
                isLandscapeMode = isLandscape
                videoSource?.sendDisplayAreaChangeResponse()
                return Protos.STATUS_SUCCESS
            }

            override fun onChannelOpened(): Int {
                permissionStatus = PERMISSION_YES
                videoSource?.sendSetup(Protos.MEDIA_CODEC_VIDEO_H264_BP)
                return Protos.STATUS_SUCCESS
            }

            override fun discoverVideoConfigCallback(
                codec: Int,
                fps: Int,
                w: Int,
                h: Int
            ): Boolean {
                val config = VideoRemoteConfig(codec, fps, w, h)
                remoteVideoConfigurations.add(config)
                return isSupportConfig(config)
            }

            override fun startResponseCallback(isOK: Boolean): Int {
                return Protos.STATUS_SUCCESS
            }

            override fun stopResponseCallback(): Int {
                return Protos.STATUS_SUCCESS
            }

            override fun configCallback(
                status: Int,
                maxUnack: Int,
                prefer: IntArray,
                size: Int
            ): Int {
                for (i in 0 until size) {
                    val config = remoteVideoConfigurations[prefer[i]]
                    if (isSupportConfig(config)) {
                        currentConfigurationIndex = prefer[i]
                        break
                    }
                }
                if (currentConfigurationIndex < 0) {
                    for (i in remoteVideoConfigurations.indices) {
                        val config = remoteVideoConfigurations[i]
                        if (isSupportConfig(config)) {
                            currentConfigurationIndex = i
                            break
                        }
                    }
                }
                val config = remoteVideoConfigurations[currentConfigurationIndex]
                currentRemoteVideoConfiguration.width = config.width
                currentRemoteVideoConfiguration.height = config.height
                currentRemoteVideoConfiguration.codec = config.codec
                currentRemoteVideoConfiguration.frameRate = config.frameRate
                isVideo = true
                appMessageListener.onVideoChannelReady()
                return Protos.STATUS_SUCCESS
            }

            override fun ackCallback(sessionId: Int, numFrames: Int): Int {
                return Protos.STATUS_SUCCESS
            }
        }
        videoSource = VideoSource(listener, autoStart, remoteHost)
    }
}