package com.link.riderservice.feature.cast.video.display

import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.projection.MediaProjection
import android.os.Handler
import android.view.Display
import android.view.Surface
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.feature.cast.video.VideoDefaults

/**
 * 管理 VirtualDisplay 和 MediaProjection 生命周期与回调桥接。
 */
internal class VirtualDisplayController(
    private val displayManager: DisplayManager,
    private val backgroundHandler: Handler,
    private val onDisplayInitialized: (Display) -> Unit,
    private val onDisplayReleased: (Display) -> Unit,
    private val onMirrorStart: () -> Unit,
    private val onMirrorStop: () -> Unit,
    private val onPresentationStart: () -> Unit,
    private val onPresentationStop: () -> Unit
) {
    private var virtualDisplay: VirtualDisplay? = null
    private var mediaProjection: MediaProjection? = null

    private val mediaProjectionCallback = object : MediaProjection.Callback() {
        override fun onStop() {
            super.onStop()
            release()
            onMirrorStop()
        }
    }

    fun attachMediaProjection(projection: MediaProjection?) {
        mediaProjection = projection
    }

    fun createMirror(surface: Surface, width: Int, height: Int, densityDpi: Int) {
        mediaProjection?.registerCallback(mediaProjectionCallback, backgroundHandler)
        virtualDisplay = mediaProjection?.createVirtualDisplay(
            VideoDefaults.MIRROR_DISPLAY_NAME,
            width,
            height,
            densityDpi,
            DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC,
            surface,
            null,
            null
        )
        onMirrorStart()
        logD(TAG, "Mirror VirtualDisplay created ${width}x${height}")
    }

    fun createPresentation(surface: Surface, width: Int, height: Int, densityDpi: Int) {
        virtualDisplay = displayManager.createVirtualDisplay(
            VideoDefaults.PRESENTATION_DISPLAY_NAME,
            width,
            height,
            densityDpi,
            surface,
            DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION
        )
        virtualDisplay?.display?.let(onDisplayInitialized)
        onPresentationStart()
        logD(TAG, "Presentation VirtualDisplay created ${width}x${height}")
    }

    @Synchronized
    fun release() {
        virtualDisplay?.let { vd ->
            try {
                onDisplayReleased(vd.display)
                vd.release()
            } catch (e: Exception) {
                logE(TAG, "Error releasing virtual display", e)
            }
        }
        virtualDisplay = null
    }

    fun detachMediaProjection() {
        try {
            mediaProjection?.stop()
            onMirrorStop()
        } catch (e: Exception) {
            logE(TAG, "Error stopping media projection", e)
        }
        mediaProjection = null
    }

    fun hasMediaProjection(): Boolean = mediaProjection != null

    companion object {
        private const val TAG = "VirtualDisplayController"
    }
}


