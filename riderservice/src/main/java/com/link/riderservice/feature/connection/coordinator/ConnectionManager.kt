package com.link.riderservice.feature.connection.coordinator

import android.app.Application
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.media.projection.MediaProjection
import android.net.ConnectivityManager
import android.net.LinkProperties
import android.net.NetworkCapabilities
import android.view.Display
import com.link.riderservice.api.BleStatus
import com.link.riderservice.api.Connection
import com.link.riderservice.api.SPNaviDayOrNight
import com.link.riderservice.api.SPNaviMode
import com.link.riderservice.api.SPWifiInfo
import com.link.riderservice.api.SocketStatus
import com.link.riderservice.api.WifiStatus
import com.link.riderservice.api.callback.BleListCallback
import com.link.riderservice.api.callback.BleStateCallback
import com.link.riderservice.api.callback.ChangeNaviModeCallback
import com.link.riderservice.api.callback.ConnectionStatusCallback
import com.link.riderservice.api.callback.NaviDayOrNightChangeCallback
import com.link.riderservice.api.callback.PermissionDetectionCallback
import com.link.riderservice.api.callback.PresentationCallback
import com.link.riderservice.api.callback.ReceiveCommonDataCallback
import com.link.riderservice.api.callback.SPRiderServicesExceptionNotiCallback
import com.link.riderservice.api.callback.WifiInfoCallback
import com.link.riderservice.api.dto.DeviceConfig
import com.link.riderservice.api.dto.WifiConnectionMode
import com.link.riderservice.api.exception.AuthException
import com.link.riderservice.api.exception.BluetoothException
import com.link.riderservice.api.exception.DeviceException
import com.link.riderservice.api.exception.NetworkException
import com.link.riderservice.api.exception.ProtocolException
import com.link.riderservice.api.exception.SPRiderServicesException
import com.link.riderservice.api.exception.WifiException
import com.link.riderservice.core.extensions.collectWithScope
import com.link.riderservice.core.extensions.mainScope
import com.link.riderservice.core.extensions.setState
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.core.logging.logI
import com.link.riderservice.core.logging.logW
import com.link.riderservice.core.utils.AppBackgroundManager
import com.link.riderservice.core.utils.BleUtils
import com.link.riderservice.core.utils.ScreenBrightnessUtils
import com.link.riderservice.core.utils.countDownByFlow
import com.link.riderservice.data.local.ConfigPreferences
import com.link.riderservice.feature.analytics.ConnectionAnalytics
import com.link.riderservice.feature.analytics.SimpleConnectionLogger
import com.link.riderservice.feature.cast.callback.CastCallback
import com.link.riderservice.feature.cast.manager.CastManager
import com.link.riderservice.feature.connection.auth.AuthenticationManager
import com.link.riderservice.feature.connection.ble.RiderBleManager
import com.link.riderservice.feature.connection.ble.model.BleDevice
import com.link.riderservice.feature.connection.recovery.ConnectionRecoveryManager
import com.link.riderservice.feature.connection.transport.tcp.TcpServerConnection.Companion.LISTEN_PORT
import com.link.riderservice.feature.connection.wifi.WifiConnectionManager
import com.link.riderservice.feature.messaging.MessageCallback
import com.link.riderservice.feature.messaging.MessageManager
import com.link.riderservice.feature.messaging.toByteArray
import com.link.riderservice.libs.ble.data.Data
import com.link.riderservice.protobuf.RiderProtocol
import com.link.riderservice.protobuf.RiderProtocol.NaviDayOrNight
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.net.Inet4Address
import java.util.Timer
import java.util.TimerTask

internal object ConnectionManager {
    private const val TAG = "ConnectionManager"
    private const val NETWORK_ERROR = -1

    private val mutableConnectionStatus = MutableStateFlow(Connection())
    private val connectionStatus = mutableConnectionStatus.asStateFlow()

    private val bleListCallbacks: MutableList<WeakReference<BleListCallback>> = mutableListOf()
    private val bleStateCallbacks: MutableList<WeakReference<BleStateCallback>> = mutableListOf()
    private val wifiInfoCallbacks: MutableList<WeakReference<WifiInfoCallback>> = mutableListOf()
    private val changeNaviModeCallbacks: MutableList<WeakReference<ChangeNaviModeCallback>> =
        mutableListOf()
    private val connectionStatusCallbacks: MutableList<WeakReference<ConnectionStatusCallback>> =
        mutableListOf()
    private val permissionDetectionCallbacks: MutableList<WeakReference<PermissionDetectionCallback>> =
        mutableListOf()
    private val riderServicesExceptionNotiCallbacks: MutableList<WeakReference<SPRiderServicesExceptionNotiCallback>> =
        mutableListOf()
    private val receiveCommonDataCallbacks: MutableList<WeakReference<ReceiveCommonDataCallback>> =
        mutableListOf()
    private val presentationCallbacks: MutableList<WeakReference<PresentationCallback>> =
        mutableListOf()
    private val naviDayOrNightChangeCallbacks: MutableList<WeakReference<NaviDayOrNightChangeCallback>> =
        mutableListOf()


    private var application: Application? = null
    private var wifiConnectionManager: WifiConnectionManager? = null
    private var castManager: CastManager = CastManager(
        onTcpConnecting = {
            ConnectionAnalytics.startSocketConnect()
        }
    )
    private val connectionRecoveryManager = ConnectionRecoveryManager()


    private var isManualDisconnect = false
    private var connectionTimer: Timer? = null
    private var countDown: kotlinx.coroutines.Job? = null
    private var isAutolinkServiceStarted = false


    private var wifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT
    private var spWifiInfo = SPWifiInfo(WifiConnectionMode.WIFI_AP_CLIENT, "", "")


    private var deviceConfig: DeviceConfig? = null
    private var productKey = "请在连接平台后查看"
    private var uuid = "请在连接平台后查看"
    private var sdkVersion = "请在连接平台后查看"
    private var protocolVersion = "请在连接平台后查看"


    private var currentNaviMode: SPNaviMode = SPNaviMode.SPNaviModeNoNavi
    private var previousNaviMode: SPNaviMode = SPNaviMode.SPNaviModeNoNavi


    private val messageCallback = object : MessageCallback {
        override fun onMessage(type: Int, msg: ByteArray?) {
            when (type) {
                RiderProtocol.NaviMessageId.MSG_WIFI_INFO_NOTIFICATION_VALUE -> {
                    handleWifiInfo(msg)
                }

                RiderProtocol.NaviMessageId.MSG_AUTOLINK_STATE_NOTIFICATION_VALUE -> {
                    handleAutolinkState(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_RESPONSE_VALUE -> {
                    handleNaviResponse(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_CHANGE_VALUE -> {
                    val naviModeChange = RiderProtocol.NaviModeChange.parseFrom(msg)
                    sendNaviModeChange(convertProtocolNaviMode(naviModeChange.naviMode))
                }

                RiderProtocol.NaviMessageId.MSG_WEATHER_INFO_REQUEST_VALUE -> {
                    requestWeatherInfo()
                }

                RiderProtocol.NaviMessageId.MSG_CONFIG_NOTIFICATION_VALUE -> {
                    handleConfig(msg)
                }

                RiderProtocol.NaviMessageId.MSG_PROTOCOL_VERSION_NOTIFICATION_VALUE -> {
                    handleProtocolVersion(msg)
                }

                RiderProtocol.NaviMessageId.MSG_COMPLICANCE_REQUEST_VALUE -> {
                    handleCompliance(msg)
                }

                RiderProtocol.NaviMessageId.MSG_ACTIVE_REQUEST_VALUE -> {
                    handleActiveRequest(msg)
                }

                RiderProtocol.NaviMessageId.MSG_AUTHORIZATION_NOTIFICATION_VALUE -> {
                    handleAuthorization(msg)
                }

                RiderProtocol.NaviMessageId.MSG_TIME_REQUEST_VALUE -> {
                    sendTimeInfo()
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_START_RESPONSE_VALUE -> {
                    handleNaviModeStart(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_STOP_RESPONSE_VALUE -> {
                    handleNaviModeStop(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_CLOSE_CONNECTION_VALUE -> {
                    closeConnect()
                }


                RiderProtocol.NaviMessageId.MSG_NAVI_DAY_OR_NIGHT_VALUE -> {
                    handleNaviDayOrNight(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_VERSION_RESPONSE_VALUE -> {
                    sdkVersion = RiderProtocol.NaviVersionResponse.parseFrom(msg).naviVersion
                }

                RiderProtocol.NaviMessageId.MSG_BT_USERDEF_RAWDATA_REQUEST_VALUE -> {
                    handleUserDefRawDataRequest(msg)
                }

                else -> {
                    logW(TAG, "unknown message type: $type")
                }
            }
        }
    }


    private fun handleUserDefRawDataRequest(msg: ByteArray?) {
        msg?.let { value ->
            receiveCommonDataCallbacks.forEach {
                it.get()?.onReceiveCommonData(value)
            }
        }
    }


    private fun convertProtocolNaviMode(naviMode: RiderProtocol.NaviMode): SPNaviMode =
        when (naviMode) {
            RiderProtocol.NaviMode.NO_NAVI -> SPNaviMode.SPNaviModeNoNavi
            RiderProtocol.NaviMode.SIMPLE_NAVI -> SPNaviMode.SPNaviModeSimpleNavi
            RiderProtocol.NaviMode.SCREEN_NAVI -> SPNaviMode.SPNaviModeScreenNavi
            RiderProtocol.NaviMode.MIRROR_NAVI -> SPNaviMode.SPNaviModeMirrorNavi
            RiderProtocol.NaviMode.CRUISE_NAVI -> SPNaviMode.SPNaviModeCruiseNavi
            RiderProtocol.NaviMode.LOCK_SCREEN_NAVI -> SPNaviMode.SPNaviModeLockScreenNavi
            else -> SPNaviMode.SPNaviModeDefaultNavi
        }


    private fun handleWifiInfo(message: ByteArray?) {
        val wifiInfo = RiderProtocol.WifiInfoNotification.parseFrom(message)
        logD(
            TAG,
            "Handle WiFi info: $wifiConnectionMode SSID: ${wifiInfo.name}, Password: ${wifiInfo.password}"
        )
        spWifiInfo = SPWifiInfo(wifiConnectionMode, wifiInfo.name, wifiInfo.password)
        if (wifiConnectionManager == null) {
            initializeWifiManager(wifiConnectionMode)
        }

        when (wifiConnectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                logD(TAG, "Using AP connection mode")
                if (wifiInfo.name.isNotEmpty()) {
                    application?.let {
                        wifiConnectionManager?.connectToInstrumentWifi(
                            wifiInfo.name,
                            wifiInfo.password
                        )
                    }
                } else {
                    logE(TAG, "AP Mode: Invalid AP WiFi info received (SSID is empty)")
                }
            }

            WifiConnectionMode.WIFI_P2P -> {
                logD(TAG, "Using P2P connection mode")
                application?.let {
                    wifiConnectionManager?.startSearchWifiAndConnect(
                        it.applicationContext,
                        wifiInfo.address,
                        wifiInfo.port
                    )
                }
            }
        }
    }

    private fun handleConfig(message: ByteArray?) {
        val config = RiderProtocol.ConfigNotification.parseFrom(message)
        deviceConfig = DeviceConfig(
            isSupportDvr = config.isSupportDvr,
            isSupportNavi = config.isSupportNavi,
            isSupportScreenNavi = config.isSupportScreenNavi,
            isSupportWeather = config.isSupportWeaNoti,
            isSupportNotification = config.isSupportAndroidNoti,
            isSupportCircularScreen = config.isSupportCircularScreen,
            isSupportCruise = config.isSupportCruiseNavi,
            isSupportMirror = config.isSupportMirrorScreen
        )
    }

    private fun handleAutolinkState(message: ByteArray?) {
        val stateNotification = RiderProtocol.AutoLinkStateNotification.parseFrom(message)
        logD(TAG, "autolink state:${stateNotification.state}")
        if (stateNotification.state == 0) {
            startAutolinkServiceSuccess()
        } else {
            disconnectWifi()
        }
    }

    private fun handleNaviResponse(message: ByteArray?) {
        val response = RiderProtocol.NaviResponse.parseFrom(message)
        sendNaviModeChangeResponse(
            response.naviMode,
            response.isReady
        )
    }

    private fun handleProtocolVersion(message: ByteArray?, isManualConnected: Boolean = false) {
        val version = RiderProtocol.ProtocolVerNotification.parseFrom(message)
        protocolVersion = "${version.major}.${version.minor}"
        checkVersion(version, isManualConnected)
    }

    private fun handleCompliance(message: ByteArray?) {
        val complianceRequest = RiderProtocol.ComplianceRequest.parseFrom(message)
        requestVerify(
            complianceRequest.productkey,
            complianceRequest.macAddr,
            complianceRequest.uuid,
            complianceRequest.time,
            complianceRequest.licenseSign,
            complianceRequest.sign
        )
        productKey = complianceRequest.productkey
        uuid = complianceRequest.uuid
    }

    private fun handleActiveRequest(message: ByteArray?) {
        val activeRequest = RiderProtocol.ActivateRequest.parseFrom(message)
        requestActive(activeRequest)
    }

    private fun requestActive(activeRequest: RiderProtocol.ActivateRequest) =
        requestActivate(
            activeRequest.productKey,
            activeRequest.macAddr,
            activeRequest.time,
            activeRequest.sign
        )

    private fun handleAuthorization(message: ByteArray?) {
        val authorizationResult = RiderProtocol.AuthorizationResult.parseFrom(message)
        if (!authorizationResult.result) {
            riderServicesExceptionNotiCallbacks.forEach {
                it.get()
                    ?.onSPRiderServicesExceptionNoti(AuthException.AuthorizationFailed("设备授权验证失败"))
            }
            disconnect()
        }
    }

    private fun sendTimeInfo() {
        if (isBleConnected()) {
            MessageManager.sendTimeInfo()
        }
    }

    private fun handleNaviModeStart(message: ByteArray?) {
        val response = RiderProtocol.NaviModeStartResponse.parseFrom(message)
        sendNaviModeStartResponse(response.naviMode)
    }

    private fun handleNaviModeStop(message: ByteArray?) {
        val response = RiderProtocol.NaviModeStopResponse.parseFrom(message)
        sendNaviModeStopResponse(response.naviMode)
    }

    private fun handleNaviDayOrNight(message: ByteArray?) {
        val mapType = RiderProtocol.NaviModeDayOrNight.parseFrom(message)
        logD(TAG, "ChangeMap:${mapType.naviDayOrNight}")
        val naviTheme = when (mapType.naviDayOrNight) {
            NaviDayOrNight.NAVI_DAYTIME -> SPNaviDayOrNight.DAY
            NaviDayOrNight.NAVI_NIGHT -> SPNaviDayOrNight.NIGHT
            NaviDayOrNight.NAVI_AUTO -> SPNaviDayOrNight.AUTO
        }
        naviDayOrNightChangeCallbacks.forEach {
            it.get()?.onNviDayOrNightChange(naviTheme)
        }
    }

    private val bleCallback = object : RiderBleManager.BleStateListener {

        // 设备相关
        override fun onDeviceConnected(device: BluetoothDevice) {
            logD(TAG, "onDeviceConnected")
            mutableConnectionStatus.setState { copy(btStatus = BleStatus.DeviceConnected(device)) }
            loopWrite()
            isManualDisconnect = false
        }

        override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
            logD(TAG, "onDeviceDisconnected: $reason")
            cancelConnectWithTimeout()
            MessageManager.clearMessage()
            RiderBleManager.release(isManualDisconnect)
            mutableConnectionStatus.setState {
                copy(btStatus = BleStatus.DeviceDisconnected(device, reason))
            }
            disconnectWifi()
            countDown?.cancel()
            countDown = null
            isAutolinkServiceStarted = false
            if (!isManualDisconnect) {
                startScan()
            }
        }

        override fun onDeviceConnectionFailed(device: BluetoothDevice, reason: Int) {
            logD(TAG, "onDeviceFailedToConnect: $reason")
            ConnectionAnalytics.finishConnection(false, "BLE connection failed: reason $reason")
            mutableConnectionStatus.setState {
                copy(btStatus = BleStatus.DeviceFailedToConnect(device, reason))
            }
        }

        // 扫描相关
        override fun onScanResults(devices: List<BleDevice>) {
            bleListCallbacks.forEach {
                it.get()?.onBleListResult(devices)
            }
        }

        // 数据相关
        override fun onDataReceived(device: BluetoothDevice, data: Data) {
            data.value?.let { buffer ->
                MessageManager.enqueueIncoming(buffer, buffer.size)
            }
        }

        // 权限和错误相关
        override fun onBluetoothDisabled() {
            riderServicesExceptionNotiCallbacks.forEach {
                it.get()?.onSPRiderServicesExceptionNoti(BluetoothException.BluetoothDisabled())
            }
        }

        override fun onLocationPermissionRequired() {
            permissionDetectionCallbacks.forEach {
                it.get()?.onNeedLocationPermission()
            }
        }

        override fun onBluetoothScanPermissionRequired() {
            permissionDetectionCallbacks.forEach {
                it.get()?.onNeedBluetoothScanPermission()
            }
        }

        override fun onNotificationFailed(device: BluetoothDevice, status: Int) {
            ConnectionAnalytics.finishConnection(false, "BLE notification failed: status $status")
            riderServicesExceptionNotiCallbacks.forEach {
                it.get()
                    ?.onSPRiderServicesExceptionNoti(DeviceException.FeatureNotSupported("BLE通知功能"))
            }
            disconnect()
        }
    }


    fun connectBle(device: BleDevice, context: Context) {
        RiderBleManager.connect(device.device, context)
    }

    @Synchronized
    fun disconnect(isManual: Boolean = true) {
        isManualDisconnect = isManual
        MessageManager.clearMessage()
        RiderBleManager.release()
        castManager.sendByeByeRequest()
        castManager.shutdown()
        wifiConnectionManager?.disconnect()
        countDown?.cancel()
        countDown = null
        isAutolinkServiceStarted = false
    }

    fun closeConnect() {
        MessageManager.clearMessage()
        RiderBleManager.closeConnect()
    }

     fun startScan(shouldAutoConnect: Boolean = true) {
        ConnectionAnalytics.startConnection("FULL_CONNECTION")

        RiderBleManager.startScan(shouldAutoConnect)
    }


     fun stopScan() {
        RiderBleManager.stopScan()
    }

     fun getBleList(): List<BleDevice> {
        return RiderBleManager.getBleList()
    }


     fun isBleConnected(): Boolean {
        return RiderBleManager.isConnected()
    }

    private fun loopWrite() {
        mainScope.launch(Dispatchers.IO) {
            while (RiderBleManager.isConnected()) {
                MessageManager.dequeueOutgoing()?.let {
                    RiderBleManager.write(it.toByteArray())
                }
            }
            RiderBleManager.release(false)
        }
    }

    private fun startSearchWifiAndConnect(context: Context, address: String, port: Int) {
        if (mutableConnectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
            logD(TAG, "wifi already connected")
            return
        }
        ConnectionAnalytics.startWifiScan()
        val configPrefs = ConfigPreferences.getInstance(context)
        val bleAddress = RiderBleManager.getConnectAddress()
        if (bleAddress.isNotEmpty()) {
            configPrefs.setBleAddress(bleAddress)
        }

        configPrefs.setWifiAddress(address)
        configPrefs.setWifiPort(port)

        logD(TAG, "P2P connect address:$address port:$port")
        wifiConnectionManager?.startSearchWifiAndConnect(context, address, port)
    }

    private fun getP2pIpAddress(): String {
        val connectivityManager =
            application?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        for (network in connectivityManager.allNetworks) {
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            if (capabilities != null && capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
                && capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_WIFI_P2P)
            ) {
                val linkProperties: LinkProperties? = connectivityManager.getLinkProperties(network)
                linkProperties?.linkAddresses?.forEach { linkAddress ->
                    val address = linkAddress.address
                    if (address is Inet4Address) {
                        return address.hostAddress ?: ""
                    }
                }
            }
        }
        return ""
    }

    private fun sendPhoneIpThroughBle(ipAddress: String) {
        try {
            MessageManager.sendAutoLinkConnect(ipAddress)
            logD(TAG, "Phone IP sent via BLE: $ipAddress:$LISTEN_PORT")
        } catch (e: Exception) {
            logE(TAG, "Failed to send IP address via BLE", e)
        }
    }

     fun requestWifiInfo(isReset: Boolean = true) {
        if (mutableConnectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            logD(TAG, "WiFi info request")
            val wifiMode = when (wifiConnectionMode) {
                WifiConnectionMode.WIFI_AP_CLIENT -> RiderProtocol.WifiMode.WIFI_AP
                WifiConnectionMode.WIFI_P2P -> RiderProtocol.WifiMode.WIFI_P2P
            }

            logD(TAG, "Requesting WiFi info for mode: $wifiConnectionMode")
            MessageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
                RiderProtocol.WifiInfoRequest
                    .newBuilder()
                    .setWifiMode(wifiMode)
                    .setIsResetWifi(isReset).build()
            )
        }
    }

    private fun disconnectWifi() {
        wifiConnectionManager?.disconnect()
    }

     fun resetWifiConnectionState() {
        logD(TAG, "Resetting WiFi connection state")
        wifiConnectionManager?.resetConnectionState()
        updateWifiStatus(WifiStatus.IDLE)
    }


     fun getWifiConnectionInfo(): String {
        return wifiConnectionManager?.getConnectionInfo() ?: "No WiFi connection manager"
    }


     fun checkWifiPermissions(): Boolean {
        return wifiConnectionManager?.checkPermissions() ?: false
    }


     fun isWifiEnabled(): Boolean {
        return wifiConnectionManager?.isWifiEnabled() ?: false
    }


    private fun updateWifiStatus(status: WifiStatus) {
        val currentStatus = mutableConnectionStatus.value
        val newStatus = currentStatus.copy(wifiStatus = status)
        mutableConnectionStatus.value = newStatus
        logD(TAG, "WiFi status updated to: $status")
    }

    /**
     * 投屏连接回调处理
     */
    private val castCallback = object : CastCallback {
        override fun onDeviceConnected() {
            logD(TAG, "Device connected (TCP/Transport layer established)")
            ConnectionAnalytics.endSocketConnect()
            ConnectionAnalytics.finishConnection(true)
            application?.let { application ->
                val configPrefs = ConfigPreferences.getInstance(application)

                if (wifiConnectionMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                    // 从 WifiConnectionManager 获取当前连接的 AP 凭据
                    val (apSsid, apPassword) = wifiConnectionManager?.getCurrentConnectedApCredentials()
                        ?: Pair(null, null)
                    if (apSsid != null && apPassword != null) {
                        logD(TAG, "AP Mode fully connected. Saving credentials: SSID=$apSsid")
                        logD(TAG, "TCP/Transport layer established")
                        configPrefs.setApSsid(apSsid)
                        configPrefs.setApPassword(apPassword)
                    } else {
                        logW(TAG, "AP Mode connected, but no AP credentials to save.")
                    }
                }

                // 保存 BLE 凭据（从 RiderBleManager 获取）
                val bleAddress = RiderBleManager.getConnectAddress()
                if (bleAddress.isNotEmpty()) {
                    logD(TAG, "Saving BLE credentials: address=$bleAddress")
                    configPrefs.setBleAddress(bleAddress)
                }
            }

            // 清除 WifiConnectionManager 中的临时凭据
            wifiConnectionManager?.clearCurrentConnectedApCredentials()

            logD(TAG, "wifi socket connected")
            castManager.start()
            mutableConnectionStatus.setState {
                copy(
                    wifiStatus = WifiStatus.DeviceConnected,
                    socketStatus = SocketStatus.Connected
                )
            }
        }

        override fun onDeviceDisconnected() {
            logD(TAG, "wifi socket disconnected")
            mutableConnectionStatus.setState {
                copy(
                    wifiStatus = WifiStatus.DeviceDisconnected,
                    socketStatus = SocketStatus.Disconnected
                )
            }
            requestWifiInfo(false)
        }

        override fun onDisplayInitialized(display: Display) {
            presentationCallbacks.forEach {
                it.get()?.onPresentationDisplayReady(display)
            }
        }

        override fun onDisplayReleased(display: Display) {
            presentationCallbacks.forEach {
                it.get()?.onPresentationDisplayReleased(display)
            }
        }

        override fun onVideoChannelReady() {
            //todo
            logD(TAG, "onVideoChannelReady")
        }

        override fun onRequestMediaProjection() {
            presentationCallbacks.forEach {
                it.get()?.onMediaProjectionPermissionRequired()
            }
        }

        override fun onMirrorStart() {
            //todo 维护mirror 的状态
            logD(TAG, "onMirrorStart")
        }

        override fun onMirrorStop() {
            //todo 维护mirror 的状态
            logD(TAG, "onMirrorStop")
        }

        override fun onTcpConnectionFailed(reason: Int) {
            logE(TAG, "TCP connection failed: $reason")

            // 记录TCP连接失败，结束整个连接会话
            ConnectionAnalytics.finishConnection(false, "TCP connection failed: reason $reason")

            mutableConnectionStatus.setState { copy(socketStatus = SocketStatus.Disconnected) }
            // 触发TCP连接恢复机制
            val errorType = mapTcpErrorToRecoveryType(reason)
            application?.let {
                connectionRecoveryManager.handleConnectionError(
                    errorType,
                    it,
                    this@ConnectionManager
                )
            }
        }

        override fun onRawDataRequest(msg: ByteArray) {
            receiveCommonDataCallbacks.forEach {
                it.get()?.onReceiveCommonData(msg)
            }
        }

        override fun onRawDataResponse() {
            //todo
            logD(TAG, "onRawDataResponse")
        }
    }

    private fun setNaviMode(naviMode: SPNaviMode) {
        castManager.setNaviMode(naviMode)
    }

     fun setMediaProjection(mediaProjection: MediaProjection) {
        castManager.setMediaProjection(mediaProjection)
    }


    private fun startConnectionEstablishing() {
        castManager.startConnectionEstablishing()
    }


    private fun handleP2pConnectionExist(logMessage: String) {
        logD(TAG, logMessage)
        ConnectionAnalytics.endWifiScan()
        ConnectionAnalytics.startWifiConnect()
        ConnectionAnalytics.endWifiConnect()

        startConnectionEstablishing()
        sendPhoneIpThroughBle(getP2pIpAddress())
        waitForAutoLinkConnect(2)
    }

    private fun requestProtocolVersion() {
        if (mutableConnectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            MessageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_PROTOCOL_VERSION_REQUEST_VALUE,
                RiderProtocol.ProtocolVerRequest.newBuilder().build()
            )
            connectWithTimeout()
        }
    }

    private fun connectWithTimeout() {
        if (connectionTimer == null) {
            connectionTimer = Timer()
        }
        connectionTimer?.schedule(object : TimerTask() {
            override fun run() {
                ConnectionAnalytics.finishConnection(false, "Connection timeout after 3 seconds")

                riderServicesExceptionNotiCallbacks.forEach {
                    it.get()?.onSPRiderServicesExceptionNoti(NetworkException.ConnectionTimeout())
                }
                disconnect()
            }
        }, 3000)
    }

    private fun cancelConnectWithTimeout() {
        connectionTimer?.cancel()
        connectionTimer?.purge()
        connectionTimer = null
    }

    /**
     * 匹配版本
     * @param version 版本
     */
    private fun checkVersion(
        version: RiderProtocol.ProtocolVerNotification,
        isManualConnected: Boolean
    ) {
        cancelConnectWithTimeout()
        if (version.major <= RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE) {
            if (RiderBleManager.isConfigConnect() && !isManualConnected) {

                if (wifiConnectionMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                    requestWifiInfo(false)
                } else { // WIFI_P2P mode (existing logic)
                    application?.let {
                        val configPreferences = ConfigPreferences.getInstance(it.applicationContext)
                        val address = configPreferences.getWifiAddress()
                        val port = configPreferences.getWifiPort()
                        if (address != null && port != 0) {
                            if (mutableConnectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
                                logD(TAG, "P2P Mode: WiFi already connected (auto-connect)")
                                return
                            }
                            logD(
                                TAG,
                                "P2P Mode: Attempting auto-connect with saved P2P info, Address: $address"
                            )
                            // P2P模式使用ConnectionManager的方法
                            startSearchWifiAndConnect(
                                it.applicationContext,
                                address,
                                port
                            )

                        } else {
                            logD(
                                TAG,
                                "P2P Mode: No saved P2P info for auto-connect, requesting info."
                            )
                            requestWifiInfo(false) // Request info if no saved P2P credentials
                        }
                    }
                }
            } else { // Auto-connect disabled or manual click
                logD(TAG, "Auto-connect disabled or manual BLE item click, requesting WiFi info.")
                requestWifiInfo(false)
            }
        } else {
            // 记录协议版本不匹配，结束整个连接会话
            ConnectionAnalytics.finishConnection(
                false,
                "Protocol version mismatch: expected ${RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE}.${RiderProtocol.ProtocolVersion.MINOR_VERSION_VALUE}, got ${version.major}.${version.minor}"
            )

            riderServicesExceptionNotiCallbacks.forEach {
                it.get()?.onSPRiderServicesExceptionNoti(
                    ProtocolException.VersionMismatch(
                        "${RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE}.${RiderProtocol.ProtocolVersion.MINOR_VERSION_VALUE}",
                        "${version.major}.${version.minor}"
                    )
                )
            }
            disconnect()
        }
    }

    private fun requestActivate(
        sdkKey: String, macAddr: String, timestamp: String, sign: String
    ) {
        mainScope.launch {
            AuthenticationManager.requestActivate(
                sdkKey = sdkKey,
                macAddr = macAddr,
                timestamp = timestamp,
                sign = sign,
                callback = object :
                    AuthenticationManager.AuthCallback<com.link.riderservice.domain.model.Authorization> {
                    override fun onSuccess(result: com.link.riderservice.domain.model.Authorization) {
                        val notification = RiderProtocol.ActivateNotification.newBuilder()
                            .setResult(result.status)
                            .setUUID(result.uuid)
                            .build()
                        MessageManager.sendActiveNotification(notification)
                    }

                    override fun onFailure(exception: SPRiderServicesException) {
                        val notification = RiderProtocol.ActivateNotification.newBuilder()
                            .setResult(NETWORK_ERROR)
                            .setUUID("")
                            .build()
                        MessageManager.sendActiveNotification(notification)

                        notifyException(exception)
                        disconnect()
                    }
                }
            )
        }
    }

    // =================================================================================
    // 私有方法 - 认证管理
    // =================================================================================

    private fun requestVerify(
        productKey: String,
        address: String,
        uuid: String,
        timestamp: String,
        licenseSign: String,
        sign: String
    ) {
        mainScope.launch {
            AuthenticationManager.requestVerify(
                productKey = productKey,
                address = address,
                uuid = uuid,
                timestamp = timestamp,
                licenseSign = licenseSign,
                sign = sign,
                callback = object :
                    AuthenticationManager.AuthCallback<com.link.riderservice.domain.model.CheckInfo> {
                    override fun onSuccess(result: com.link.riderservice.domain.model.CheckInfo) {
                        val notification = RiderProtocol.ComplianceNotification.newBuilder()
                            .setResult(result.status)
                            .build()
                        MessageManager.sendComplianceNotification(notification)
                        requestProtocolVersion()
                    }

                    override fun onFailure(exception: SPRiderServicesException) {
                        val notification = RiderProtocol.ComplianceNotification.newBuilder()
                            .setResult(NETWORK_ERROR)
                            .build()
                        MessageManager.sendComplianceNotification(notification)

                        notifyException(exception)
                        when (exception) {
                            is NetworkException -> disconnect(false)
                            else -> disconnect()
                        }
                    }
                }
            )
        }
    }


    private fun notifyException(exception: SPRiderServicesException) {
        riderServicesExceptionNotiCallbacks.forEach { callbackRef ->
            callbackRef.get()?.onSPRiderServicesExceptionNoti(exception)
        }
    }

    private fun waitForAutoLinkConnect(retryCount: Int) {
        countDown = countDownByFlow(
            10, 1000, mainScope,
            onTick = {
                logD(TAG, "次数：$retryCount,倒计时：$it,Autolink启动情况:$isAutolinkServiceStarted")
                if (isAutolinkServiceStarted || wifiConnectionManager?.isP2pConnected() != true) {
                    countDown?.cancel()
                }
            }, onFinish = {
                if (!isAutolinkServiceStarted) {
                    if (retryCount == 6) {
                        logD(TAG, "AutoLink 启动失败，屏蔽投屏导航")
                    } else {
                        sendPhoneIpThroughBle(getP2pIpAddress())
                        waitForAutoLinkConnect(retryCount + 1)
                    }
                }
            })
    }

    /**
     * Autolink启动成功
     */
    private fun startAutolinkServiceSuccess() {
        isAutolinkServiceStarted = true
    }

    /**
     * 连接恢复回调处理
     */
    private val recoveryListener = object : ConnectionRecoveryManager.ErrorHandlerListener {
        override fun onRecoveryStarted(errorType: ConnectionRecoveryManager.ErrorType) {
            logI(TAG, "Connection recovery started for error: $errorType")
        }

        override fun onRecoveryProgress(attempt: Int, maxAttempts: Int) {
            logI(TAG, "Recovery progress: $attempt/$maxAttempts")
        }

        override fun onRecoverySuccess(strategy: ConnectionRecoveryManager.RecoveryStrategy) {
            logI(TAG, "Connection recovery succeeded with strategy: $strategy")
        }

        override fun onRecoveryFailed(
            errorType: ConnectionRecoveryManager.ErrorType,
            finalStrategy: ConnectionRecoveryManager.RecoveryStrategy
        ) {
            logE(
                TAG,
                "Connection recovery failed for error: $errorType, final strategy: $finalStrategy"
            )
        }

        override fun onUserGuidanceRequired(
            errorType: ConnectionRecoveryManager.ErrorType,
            guidance: String
        ) {
            logW(TAG, "User guidance required for error: $errorType, guidance: $guidance")
            //todo wifi exception
        }
    }

    /**
     * 将WiFi错误代码映射到恢复错误类型
     */
    private fun mapWifiErrorToRecoveryType(reason: Int): ConnectionRecoveryManager.ErrorType {
        return when (reason) {
            -1 -> ConnectionRecoveryManager.ErrorType.NETWORK_UNAVAILABLE
            -2 -> ConnectionRecoveryManager.ErrorType.AUTHENTICATION_FAILED
            -3 -> ConnectionRecoveryManager.ErrorType.CONNECTION_TIMEOUT
            -4 -> ConnectionRecoveryManager.ErrorType.WIFI_DISABLED
            -5 -> ConnectionRecoveryManager.ErrorType.PERMISSION_ERROR
            -6 -> ConnectionRecoveryManager.ErrorType.IP_ACQUISITION_FAILED
            else -> ConnectionRecoveryManager.ErrorType.UNKNOWN_ERROR
        }
    }

    /**
     * 将TCP错误代码映射到恢复错误类型
     */
    private fun mapTcpErrorToRecoveryType(reason: Int): ConnectionRecoveryManager.ErrorType {
        return when (reason) {
            -7 -> ConnectionRecoveryManager.ErrorType.TCP_CONNECTION_FAILED
            -8 -> ConnectionRecoveryManager.ErrorType.CONNECTION_TIMEOUT
            -9 -> ConnectionRecoveryManager.ErrorType.NETWORK_UNAVAILABLE
            else -> ConnectionRecoveryManager.ErrorType.TCP_CONNECTION_FAILED
        }
    }

    /**
     * 手动触发连接恢复
     */
    fun triggerConnectionRecovery(errorType: ConnectionRecoveryManager.ErrorType) {
        application?.let {
            connectionRecoveryManager.handleConnectionError(errorType, it.applicationContext, this)
        }
    }

    /**
     * 停止连接恢复
     */
    fun stopConnectionRecovery() {
        connectionRecoveryManager.stopRecovery()
    }

    /**
     * 获取连接恢复状态
     */
    fun isConnectionRecovering(): Boolean {
        return connectionRecoveryManager.isRecovering()
    }

    /**
     * 获取当前恢复尝试次数
     */
    fun getCurrentRecoveryAttempts(): Int {
        return connectionRecoveryManager.getCurrentRecoveryAttempts()
    }

    // =================================================================================
    // Service Callback Forwarding
    // =================================================================================

    /**
     * 导航模式发改变
     * @param mode 模式
     * @see SPNaviMode
     */
     fun sendNaviModeChange(mode: SPNaviMode) {
        //todo 功能暂时不支持
    }

    private fun sendNaviModeChangeResponse(naviMode: RiderProtocol.NaviMode, ready: Boolean) {
        changeNaviModeCallbacks.forEach {
            it.get()?.onNaviModeChange(getNaviMode(naviMode), ready)
        }
    }

    private fun requestWeatherInfo() {
        //todo(请求天气信息)
    }

    private fun sendNaviModeStartResponse(naviMode: RiderProtocol.NaviMode) {
        //TODO(内部处理)
//        serviceCallbacks.forEach { callback ->
//            callback.get()?.onNaviModeStartResponse(getNaviMode(naviMode))
//        }
    }

    private fun sendNaviModeStopResponse(naviMode: RiderProtocol.NaviMode) {
        //TODO(内部处理)
//        serviceCallbacks.forEach { callback ->
//            callback.get()?.onNaviModeStopResponse(getNaviMode(naviMode))
//        }
    }

    private fun getNaviMode(naviMode: RiderProtocol.NaviMode): SPNaviMode {
        return when (naviMode) {
            RiderProtocol.NaviMode.DEFAULT_NAVI -> SPNaviMode.SPNaviModeDefaultNavi
            RiderProtocol.NaviMode.SIMPLE_NAVI -> SPNaviMode.SPNaviModeSimpleNavi
            RiderProtocol.NaviMode.SCREEN_NAVI -> SPNaviMode.SPNaviModeScreenNavi
            RiderProtocol.NaviMode.MIRROR_NAVI -> SPNaviMode.SPNaviModeMirrorNavi
            RiderProtocol.NaviMode.CRUISE_NAVI -> SPNaviMode.SPNaviModeCruiseNavi
            RiderProtocol.NaviMode.LOCK_SCREEN_NAVI -> SPNaviMode.SPNaviModeLockScreenNavi
            RiderProtocol.NaviMode.NO_NAVI -> SPNaviMode.SPNaviModeNoNavi
        }
    }

     fun getProtocolVersion(): String {
        return protocolVersion
    }


     fun getConnectStatus(): Connection {
        return mutableConnectionStatus.value
    }

     fun getCurrentConnectDevice(): BluetoothDevice? {
        return RiderBleManager.getCurrentConnectDevice()
    }


    fun getConnectionAnalyticsInfo(): String {
        val activeConnections = ConnectionAnalytics.getActiveConnectionCount()

        return buildString {
            appendLine("=== Connection Analytics ===")
            appendLine("Active connections: $activeConnections")
        }
    }


    private fun initConnectionTimeTracker(context: Context) {
        SimpleConnectionLogger.init(context)
    }


    fun clearConnectionTimeRecords() {
        SimpleConnectionLogger.clearLogs()
    }

    // =================================================================================
    // 公共 API 方法 - 生命周期管理
    // =================================================================================

    /**
     * 初始化连接管理器
     */
     fun init(application: Application) {
        this.application = application
        MessageManager.registerCallback(messageCallback)
        setupScreenStateListeners(application)
        setupAppStateListener(application)
        initConnectionTimeTracker(application.applicationContext)
        RiderBleManager.addStateListener(bleCallback)
        RiderBleManager.registerBroadcastReceivers(application.applicationContext)
        connectionRecoveryManager.setErrorHandlerListener(recoveryListener)

    }

    private fun initializeWifiManager(connectionMode: WifiConnectionMode) {
        application?.let { app ->
            wifiConnectionManager = WifiConnectionManager(
                connectionMode = connectionMode,
                callback = object : WifiConnectionManager.WifiConnectionCallback {
                    override fun onWifiStatusChanged(status: WifiStatus) {
                        updateWifiStatus(status)
                    }

                    override fun onWifiConnected(ssid: String, ipAddress: String) {
                        logD(TAG, "WiFi connected: $ssid, IP: $ipAddress")
                        updateWifiStatus(WifiStatus.DeviceConnected)
                        if (wifiConnectionMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                            wifiConnectionManager?.setCurrentConnectedApCredentials(
                                ssid,
                                spWifiInfo.password
                            )
                        }

                        // 根据连接模式选择不同的启动方式
                        when (wifiConnectionMode) {
                            WifiConnectionMode.WIFI_AP_CLIENT -> {
                                // AP 模式：启动 TCP 服务器连接
                                castManager.startTcpServerConnection(ipAddress)
                            }

                            WifiConnectionMode.WIFI_P2P -> {
                                // P2P 模式：启动连接建立过程
                                castManager.startConnectionEstablishing()
                            }
                        }

                        logD(TAG, "Sending phone IP to instrument: $ipAddress:$LISTEN_PORT")
                        sendPhoneIpThroughBle(ipAddress)
                    }

                    override fun onWifiDisconnected() {
                        logD(TAG, "WiFi disconnected")
                        updateWifiStatus(WifiStatus.DeviceDisconnected)
                        castManager.shutdown()
                        countDown?.cancel()
                        countDown = null
                        isAutolinkServiceStarted = false
                        wifiConnectionManager = null
                    }

                    override fun onWifiConnectionFailed(reason: Int) {
                        logE(TAG, "WiFi connection failed: $reason")
                        updateWifiStatus(WifiStatus.DeviceDisconnected)
                        val errorType = mapWifiErrorToRecoveryType(reason)
                        connectionRecoveryManager.handleConnectionError(
                            errorType,
                            app,
                            this@ConnectionManager
                        )
                    }

                    override fun onWifiStateChanged(enabled: Boolean) {
                        if (!enabled && connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
                            riderServicesExceptionNotiCallbacks.forEach {
                                it.get()
                                    ?.onSPRiderServicesExceptionNoti(WifiException.WifiDisabled())
                            }
                        }
                    }

                    override fun onExceptionOccurred(exception: SPRiderServicesException) {
                        notifyException(exception)
                    }

                    override fun onRequestWifiInfo(isReset: Boolean) {
                        requestWifiInfo(isReset)
                    }

                    override fun onP2pConnectionEstablished() {
                        startConnectionEstablishing()
                    }

                    override fun onP2pConnectionExist(message: String) {
                        handleP2pConnectionExist(message)
                    }
                }
            )
            wifiConnectionManager?.initialize(app)
        }
    }

     fun destroy(context: Context) {
        RiderBleManager.stopScan()
        RiderBleManager.removeStateListener(bleCallback)
        RiderBleManager.unregisterBroadcastReceivers(context)
        connectionRecoveryManager.stopRecovery()
    }

     fun release() {
        disconnect()
        mutableConnectionStatus.setState {
            copy(
                wifiStatus = WifiStatus.IDLE,
                btStatus = BleStatus.IDLE,
                socketStatus = SocketStatus.IDLE
            )
        }
        castManager.removeCallback(castCallback)
        RiderBleManager.release()
    }

     fun getSDKVersion(): String {
        return sdkVersion
    }

     fun getDeviceConfig(): DeviceConfig? {
        return deviceConfig
    }

     fun setWifiConnectionMode(mode: WifiConnectionMode) {
        wifiConnectionMode = mode
        logD(TAG, "WiFi connection mode set to: $mode")
    }

     fun getWifiConnectionMode(): WifiConnectionMode {
        return wifiConnectionMode
    }

    // =================================================================================
    // 私有方法 - 工具和辅助
    // =================================================================================

    private fun setupScreenStateListeners(application: Application) {
        ScreenBrightnessUtils.addScreenListener(object :
            ScreenBrightnessUtils.OnScreenStateUpdateListener {
            override fun whenScreenOff() {
                logD(TAG, "Screen off, current mode: $currentNaviMode")
                // 屏幕关闭时，如果现在是mirror mode就切换到锁屏导航，其他模式不切
                if (currentNaviMode == SPNaviMode.SPNaviModeMirrorNavi) {
                    changeNaviMode(SPNaviMode.SPNaviModeLockScreenNavi)
                }
            }

            override fun whenScreenOn() {
                logD(TAG, "Screen on, current mode: $currentNaviMode")
            }

            override fun whenUserPresent() {
                logD(TAG, "User present, current mode: $currentNaviMode")
                // 用户解锁时，如果是锁屏模式才切换到巡航模式，其他模式不切
                if (currentNaviMode == SPNaviMode.SPNaviModeLockScreenNavi) {
                    changeNaviMode(SPNaviMode.SPNaviModeCruiseNavi)
                }
            }

        })
        ScreenBrightnessUtils.registerScreenBroadcastReceiver(application)
    }


    private fun setupAppStateListener(application: Application) {
        AppBackgroundManager.init(application)
        AppBackgroundManager.addListener(object : AppBackgroundManager.AppStateListener {
            override fun onAppStateChanged(newState: AppBackgroundManager.AppState) {
                logD(TAG, "App state changed: $newState, current mode: $currentNaviMode")

                when (newState) {
                    AppBackgroundManager.AppState.FOREGROUND -> {
                        // 应用回到前台时，如果是锁屏模式才切换到巡航模式，其他模式不切
                        if (currentNaviMode == SPNaviMode.SPNaviModeLockScreenNavi) {
                            changeNaviMode(SPNaviMode.SPNaviModeCruiseNavi)
                        }
                    }

                    AppBackgroundManager.AppState.BACKGROUND -> {
                        // 应用进入后台时不做模式切换
                        logD(TAG, "App entered background, keeping current mode: $currentNaviMode")
                    }
                }
            }
        })
    }

    // =================================================================================
    // 公共 API 方法 - 回调管理
    // =================================================================================

    @Synchronized
     fun addBleListCallback(callback: BleListCallback) {
        bleListCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removeBleListCallback(callback: BleListCallback) {
        bleListCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addBleConnectStateCallback(callback: BleStateCallback) {
        bleStateCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removeBleConnectStateCallback(callback: BleStateCallback) {
        bleStateCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addWifiInfoCallback(callback: WifiInfoCallback) {
        wifiInfoCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removeWifiInfoCallback(callback: WifiInfoCallback) {
        wifiInfoCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addChangeNaviModeCallback(callback: ChangeNaviModeCallback) {
        changeNaviModeCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removeChangeNaviModeCallback(callback: ChangeNaviModeCallback) {
        changeNaviModeCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addConnectionStatusCallback(callback: ConnectionStatusCallback) {
        connectionStatusCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removeConnectionStatusCallback(callback: ConnectionStatusCallback) {
        changeNaviModeCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addPermissionDetectionCallback(callback: PermissionDetectionCallback) {
        permissionDetectionCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removePermissionDetectionCallback(callback: PermissionDetectionCallback) {
        permissionDetectionCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addRiderServicesExceptionNotiCallback(callback: SPRiderServicesExceptionNotiCallback) {
        riderServicesExceptionNotiCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removeRiderServicesExceptionNotiCallback(callback: SPRiderServicesExceptionNotiCallback) {
        riderServicesExceptionNotiCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addReceiveCommonDataCallback(callback: ReceiveCommonDataCallback) {
        receiveCommonDataCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removeReceiveCommonDataCallback(callback: ReceiveCommonDataCallback) {
        receiveCommonDataCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addPresentationCallback(callback: PresentationCallback) {
        presentationCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removePresentationCallback(callback: PresentationCallback) {
        presentationCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
     fun addNaviDayOrNightChangeCallback(callback: NaviDayOrNightChangeCallback) {
        naviDayOrNightChangeCallbacks.add(WeakReference(callback))
    }

    @Synchronized
     fun removeNaviDayOrNightChangeCallback(callback: NaviDayOrNightChangeCallback) {
        naviDayOrNightChangeCallbacks.removeIf { it.get() == callback }
    }

     fun connectWifi() {
        TODO("连接wifi")
    }

     fun getWifiInfo(): SPWifiInfo {
        return spWifiInfo
    }

    // =================================================================================
    // 公共 API 方法 - 导航模式管理
    // =================================================================================

    /**
     * 导航状态查询方法
     */
     fun getCurrentNaviMode(): SPNaviMode = currentNaviMode
     fun getPreviousNaviMode(): SPNaviMode = previousNaviMode

    // Convenience methods for checking specific modes
     fun isMirrorMode(): Boolean = currentNaviMode == SPNaviMode.SPNaviModeMirrorNavi
     fun isCruiseMode(): Boolean = currentNaviMode == SPNaviMode.SPNaviModeCruiseNavi
     fun isScreenNaviMode(): Boolean = currentNaviMode == SPNaviMode.SPNaviModeScreenNavi

     fun startNaviMode(naviMode: SPNaviMode) {
        logD(TAG, "startNaviMode: $currentNaviMode -> $naviMode")

        if (isBleConnected()) {
            // 保存上一个模式
            previousNaviMode = currentNaviMode
            currentNaviMode = naviMode

            MessageManager.startNaviMode(naviMode)

            // 通知状态变化
            notifyNaviModeChanged(previousNaviMode, currentNaviMode)
        }
    }

     fun changeNaviMode(naviMode: SPNaviMode) {
        logD(TAG, "changeNaviMode: $currentNaviMode -> $naviMode")

        if (isBleConnected()) {
            previousNaviMode = currentNaviMode
            currentNaviMode = naviMode
            // todo
            //MessageManager.changeNaviMode(naviMode)
        }
    }

     fun stopNaviMode(naviMode: SPNaviMode) {
        logD(TAG, "stopNaviMode: $currentNaviMode -> $naviMode")

        if (isBleConnected()) {
            previousNaviMode = currentNaviMode

            // 确定停止后的模式
            currentNaviMode = when {
                // 如果停止的是当前模式，则根据配置选择默认模式
                naviMode == currentNaviMode -> {
                    if (getDeviceConfig()?.isSupportCruise == true) {
                        SPNaviMode.SPNaviModeCruiseNavi
                    } else {
                        SPNaviMode.SPNaviModeNoNavi
                    }
                }
                // 否则保持当前模式不变
                else -> currentNaviMode
            }

            MessageManager.stopNaviMode(naviMode)
            notifyNaviModeChanged(previousNaviMode, currentNaviMode)
        }
    }

    private fun notifyNaviModeChanged(oldMode: SPNaviMode, newMode: SPNaviMode) {
        mainScope.launch {
            changeNaviModeCallbacks.forEach { callbackRef ->
                callbackRef.get()?.onNaviModeChanged(oldMode, newMode)
            }
        }
    }


    fun connectUseQRCodeValue(value: String) {
        logD(TAG, "Starting connection using QR code value")
        val macAddress = BleUtils.parseQRCodeForMacAddress(value)
        if (macAddress == null) {
            logE(TAG, "Failed to parse MAC address from QR code, aborting connection")
            return
        }

        application?.let { app ->
            val success = RiderBleManager.connectByMacAddress(macAddress, app)
            if (success) {
                logD(TAG, "Successfully initiated BLE connection to: $macAddress")
            } else {
                logE(TAG, "Failed to initiate BLE connection to: $macAddress")
            }
        } ?: run {
            logE(TAG, "Application context is null, cannot connect")
        }
    }

    fun sendCommonData(byteArray: ByteArray) {
        castManager.sendRawDataRequest(byteArray)
    }


    init {
        connectionStatus.collectWithScope(mainScope) { connection ->
            connectionStatusCallbacks.forEach {
                it.get()?.onConnectionStatusChanged(connection)
            }
        }
        castManager.addCallback(castCallback)
    }
}