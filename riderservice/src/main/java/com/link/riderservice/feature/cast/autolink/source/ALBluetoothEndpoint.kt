package com.link.riderservice.feature.cast.autolink.source


import com.link.riderservice.core.logging.logW
import com.link.riderservice.feature.cast.autolink.project.BluetoothCallbacks
import com.link.riderservice.feature.cast.autolink.project.BluetoothEndpoint
import com.link.riderservice.feature.cast.autolink.project.Protos
import com.link.riderservice.feature.cast.config.BluetoothConfig
import java.util.TimerTask
import java.util.concurrent.ScheduledExecutorService

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class ALBluetoothEndpoint(
    private val bluetoothConfig: BluetoothConfig,
    private val bluetoothListener: BluetoothListener
) :
    ALServiceBase {


    interface BluetoothListener {
        fun onInit(carAddress: String?)

        fun onPairingRequest(carAddress: String?, alreadyPaired: Boolean)
    }

    private var bluetoothEndpoint: BluetoothEndpoint? = null
    private var carBluetoothAddress: String? = null
    private var scheduledExecutorService: ScheduledExecutorService? = null
    fun sendBluetoothStatus(status: Int, unsolicited: Boolean) {
        bluetoothEndpoint?.nativeSendBluetoothStatus(status, unsolicited)
    }

    fun sendPairRequest(address: String?) {
        bluetoothConfig.address = address
    }

    override fun destroy() {
        if (scheduledExecutorService != null) {
            scheduledExecutorService?.shutdownNow()
            scheduledExecutorService = null
        }
        bluetoothEndpoint?.destroy()
    }

    override fun create(serviceId: Int, nativeGalReceiver: Long): Boolean {
        return bluetoothEndpoint?.create(serviceId, nativeGalReceiver) ?: false
    }

    override fun start(): Boolean {
        return true
    }

    override val serviceType: Int
        get() = ALServiceBase.AL_SERVICE_BLUETOOTH
    override val nativeInstance: Long
        get() = bluetoothEndpoint?.nativeInstance ?: 0

    private val pairingRequestTimerTask: TimerTask = object : TimerTask() {
        override fun run() {
            bluetoothEndpoint?.nativeSendPairingRequest(
                bluetoothConfig.address,
                Protos.BLUETOOTH_PAIRING_NUMERIC_COMPARISON
            )
        }
    }

    companion object {
        private const val TAG = "ALBluetoothEndpoint"
    }

    init {
        val bluetoothCallbacks: BluetoothCallbacks = object : BluetoothCallbacks {
            override fun onChannelOpened(): Int {
                bluetoothListener.onInit(carBluetoothAddress)
                return Protos.STATUS_SUCCESS
            }

            override fun discoverBluetoothService(
                carAddress: String,
                methodsBitmap: Int
            ): Boolean {
                carBluetoothAddress = carAddress
                return true
            }

            override fun onAuthenticationData(authData: String?) {
                //do nothing
            }

            override fun onPhoneBluetoothStatusInquire() {
                //do nothing
            }

            override fun onPairingResponse(status: Int, alreadyPaired: Boolean) {
                if (status == Protos.STATUS_SUCCESS) {
                    if (scheduledExecutorService != null) {
                        scheduledExecutorService?.shutdownNow()
                        scheduledExecutorService = null
                    }
                } else {
                    logW(TAG, "car's bluetooth is busy")
                }
            }
        }
        bluetoothEndpoint = BluetoothEndpoint(bluetoothCallbacks)
    }
}