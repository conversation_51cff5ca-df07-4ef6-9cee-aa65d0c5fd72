package com.link.riderservice.feature.connection.ble.scanning.filter

import android.os.ParcelUuid
import com.link.riderservice.core.utils.BleUtils
import com.link.riderservice.feature.connection.ble.scanning.parser.BeaconParser
import com.link.riderservice.libs.ble.scanner.ScanRecord
import com.link.riderservice.libs.ble.scanner.ScanResult
import java.util.UUID

/**
 * BLE 扫描过滤与匹配工具
 * - 统一管理扫描过滤的常量与判定函数
 * - 保持纯函数，便于单测与复用
 */
internal object ScanFilters {

    // 过滤器配置常量
    private const val FILTER_UUID_REQUIRED: Boolean = false
    private const val FILTER_NEARBY_ONLY: Boolean = true
    private const val FILTER_RSSI = -100 // [dBm]
    private val FILTER_SERVICE_UUID: ParcelUuid =
        ParcelUuid(UUID.fromString("0000ff00-0000-1000-8000-00805f9b34fb"))

    // 制造商数据配置
    private const val MANUFACTURE_ID = 0x0AE7
    private val MANUFACTURE_DATA = byteArrayOf(0x72, 0x6C)

    /**
     * 是否认为为噪声结果
     */
    fun isNoise(result: ScanResult): Boolean {
        return !result.isConnectable ||
                result.rssi < FILTER_RSSI ||
                BleUtils.isBeacon(result) ||
                BleUtils.isAirDrop(result)
    }

    /**
     * UUID 过滤
     */
    fun matchesUuidFilter(result: ScanResult): Boolean {
        if (!FILTER_UUID_REQUIRED) return true
        val record: ScanRecord = result.scanRecord ?: return false
        val uuids = record.serviceUuids ?: return false
        return uuids.contains(FILTER_SERVICE_UUID)
    }

    /**
     * 距离（RSSI）过滤
     */
    fun matchesNearbyFilter(rssi: Int): Boolean {
        return if (!FILTER_NEARBY_ONLY) true else (rssi in FILTER_RSSI..-1)
    }

    /**
     * 制造商数据过滤
     */
    fun matchesManufacturerFilter(scanResult: ScanResult): Boolean {
        val record: ScanRecord = scanResult.scanRecord ?: return false
        val companyData = record.getManufacturerSpecificData(MANUFACTURE_ID) ?: return false
        if (companyData.contentEquals(MANUFACTURE_DATA)) {
            return true
        }
        val beacons = BeaconParser.parseBeacon(companyData)
        if (beacons.isEmpty()) {
            return false
        }
        for (beacon in beacons) {
            if (beacon.type == 0xF9) {
                beacon.bytes?.let { bytes ->
                    if (bytes.contentEquals(MANUFACTURE_DATA)) {
                        return true
                    }
                }
            }
        }
        return false
    }

    fun manufacturerId(): Int = MANUFACTURE_ID
}


