package com.link.riderservice.feature.connection.ble.core.strategy

import com.link.riderservice.core.logging.logD
import com.link.riderservice.feature.connection.ble.model.BleDevice

/**
 * 基于 RSSI 的最近设备选择策略
 * - 要求最强设备与次强设备的差距超过阈值
 * - 要求最强设备信号强度高于下限
 */
internal class NearestRssiStrategy(
    private val minDeviceRssi: Int = -48,
    private val minRssiDifference: Int = 10,
    private val tag: String = "NearestRssiStrategy"
) : DeviceSelectionStrategy {

    override fun select(devices: List<BleDevice>): BleDevice? {
        if (devices.isEmpty()) return null
        val sortedDevices = devices.sortedByDescending { it.rssi }

        return when (sortedDevices.size) {
            0 -> null
            1 -> {
                val device = sortedDevices.first()
                if (device.rssi > minDeviceRssi) {
                    logD(tag, "Single device found with RSSI: ${device.rssi}dBm")
                    device
                } else null
            }
            else -> {
                val firstDevice = sortedDevices[0]
                val secondDevice = sortedDevices[1]
                val rssiDifference = firstDevice.rssi - secondDevice.rssi
                logD(tag, "Best: ${firstDevice.rssi}dBm, Second: ${secondDevice.rssi}dBm, Diff: ${rssiDifference}dBm")
                if (rssiDifference > minRssiDifference && firstDevice.rssi > minDeviceRssi) firstDevice else null
            }
        }
    }
}


