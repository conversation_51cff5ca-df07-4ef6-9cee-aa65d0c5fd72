package com.link.riderservice.feature.cast.autolink.project

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class BluetoothEndpoint(var callbacks: BluetoothCallbacks) :
    NativeObject {
    override val nativeInstance: Long = 0
    fun create(bluetoothServiceId: Int, nativeReceiverInstance: Long): Boolean {
        return nativeInit(bluetoothServiceId, nativeReceiverInstance) == 0
    }

    override fun destroy() {
        nativeShutdown()
    }

    @Throws(IllegalStateException::class)
    private external fun nativeInit(bluetoothServiceId: Int, nativeReceiverInstance: Long): Int
    private external fun nativeShutdown()
    external fun nativeSendPairingRequest(deviceAddress: String?, pairingMethod: Int)
    external fun nativeSendBluetoothStatus(status: Int, unsolicited: Boolean)
}