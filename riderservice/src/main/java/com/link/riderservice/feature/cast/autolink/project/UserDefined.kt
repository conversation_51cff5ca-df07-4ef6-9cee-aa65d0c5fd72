package com.link.riderservice.feature.cast.autolink.project

internal class UserDefined(var callbacks: UserDefinedCallbacks) :
    NativeObject {
    override val nativeInstance: Long = 0
    fun create(userDefinedId: Int, nativeReceiverInstance: Long): Boolean {
        return nativeInit(userDefinedId, nativeReceiverInstance) == 0
    }

    override fun destroy() {
        nativeShutdown()
    }

    @Throws(IllegalStateException::class)
    private external fun nativeInit(userDefinedId: Int, nativeReceiverInstance: Long): Int
    private external fun nativeShutdown()
    external fun nativeRawDataRequest(msg: ByteArray)
    external fun nativeRawDataResponse()
}