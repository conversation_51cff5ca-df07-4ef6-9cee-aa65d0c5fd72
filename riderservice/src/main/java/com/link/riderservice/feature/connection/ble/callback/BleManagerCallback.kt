package com.link.riderservice.feature.connection.ble.callback

import android.bluetooth.BluetoothDevice
import com.link.riderservice.libs.ble.data.Data

internal interface BleManagerCallback {
    fun onDataReceived(device: BluetoothDevice, data: Data)
    fun onDeviceConnecting(device: BluetoothDevice)
    fun onDeviceConnected(device: BluetoothDevice)
    fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int)
    fun onDeviceReady(device: BluetoothDevice)
    fun onDeviceDisconnecting(device: BluetoothDevice)
    fun onDeviceDisconnected(device: BluetoothDevice, reason: Int)
    fun onWriteRequestFailed(device: BluetoothDevice, status: Int)
    fun onEnableNotificationFailed(device: BluetoothDevice, status: Int)
}