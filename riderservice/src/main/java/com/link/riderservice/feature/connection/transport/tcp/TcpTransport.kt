package com.link.riderservice.feature.connection.transport.tcp

import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.core.logging.logW
import com.link.riderservice.feature.connection.transport.Transport
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.net.Socket
import java.net.SocketException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 优化的TCP传输层实现
 * 提供可靠的TCP数据传输功能，包含完善的错误处理和资源管理
 *
 * <AUTHOR>
 * @date 2020/4/27
 * @updated AI Assistant 2024
 */
internal class TcpTransport(
    private val socket: Socket,
    private val config: TcpConnectionConfig = TcpConnectionConfig.createDefault()
) : Transport {

    private val inputStream: InputStream = socket.getInputStream()
    private val outputStream: OutputStream = socket.getOutputStream()
    private val isClosed = AtomicBoolean(false)

    init {
        configureSocket()
        if (config.enableVerboseLogging) {
            logD(TAG, "TcpTransport initialized for ${socket.remoteSocketAddress}")
        }
    }

    /**
     * 配置Socket参数
     */
    private fun configureSocket() {
        try {
            socket.tcpNoDelay = config.tcpNoDelay
            socket.keepAlive = config.keepAlive
            socket.soTimeout = config.soTimeout

            if (config.enableVerboseLogging) {
                logD(TAG, "Socket configured: tcpNoDelay=${config.tcpNoDelay}, " +
                          "keepAlive=${config.keepAlive}, soTimeout=${config.soTimeout}")
            }
        } catch (e: SocketException) {
            logW(TAG, "Failed to configure socket parameters", e)
        }
    }

    override fun stopTransport() {
        if (isClosed.compareAndSet(false, true)) {
            try {
                if (config.enableVerboseLogging) {
                    logD(TAG, "Stopping transport for ${socket.remoteSocketAddress}")
                }

                // 按顺序关闭资源
                inputStream.close()
                outputStream.close()
                socket.close()

                if (config.enableVerboseLogging) {
                    logD(TAG, "Transport stopped successfully")
                }
            } catch (e: IOException) {
                logE(TAG, "Failed to close transport resources", e)
            }
        }
    }

    @Throws(IOException::class)
    override fun read(buffer: ByteArray, offset: Int, length: Int): Int {
        if (isClosed.get()) {
            throw IOException("Transport is closed")
        }

        try {
            val bytesRead = inputStream.read(buffer, offset, length)
            if (config.enableVerboseLogging && bytesRead > 0) {
                logD(TAG, "Read $bytesRead bytes from transport")
            }
            return bytesRead
        } catch (e: IOException) {
            logE(TAG, "Failed to read from transport", e)
            throw e
        }
    }

    @Throws(IOException::class)
    override fun write(buffer: ByteArray, offset: Int, length: Int) {
        if (isClosed.get()) {
            throw IOException("Transport is closed")
        }

        try {
            outputStream.write(buffer, offset, length)
            outputStream.flush() // 确保数据立即发送

            if (config.enableVerboseLogging) {
                logD(TAG, "Wrote $length bytes to transport")
            }
        } catch (e: IOException) {
            logE(TAG, "Failed to write to transport", e)
            throw e
        }
    }

    override fun isConnected(): Boolean {
        return !isClosed.get() && socket.isConnected && !socket.isClosed
    }

    /**
     * 获取远程地址信息
     */
    fun getRemoteAddress(): String? {
        return try {
            socket.remoteSocketAddress?.toString()
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取本地地址信息
     */
    fun getLocalAddress(): String? {
        return try {
            socket.localSocketAddress?.toString()
        } catch (e: Exception) {
            null
        }
    }

    companion object {
        private const val TAG = TcpConnectionConstants.TAG_TCP_TRANSPORT
    }
}