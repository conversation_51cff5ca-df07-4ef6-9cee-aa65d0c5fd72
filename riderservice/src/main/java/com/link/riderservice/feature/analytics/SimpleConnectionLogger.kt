package com.link.riderservice.feature.analytics

import android.content.Context
import com.link.riderservice.BuildConfig
import com.link.riderservice.core.logging.logD
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 简单的连接时间日志记录器
 * 只在Debug模式下工作，将连接时间记录到文件
 */
internal object SimpleConnectionLogger {

    private const val TAG = "SimpleConnectionLogger"
    private var appContext: Context? = null

    /**
     * 初始化
     */
    fun init(context: Context) {
        if (BuildConfig.DEBUG) {
            appContext = context.applicationContext
            logD(TAG, "SimpleConnectionLogger initialized in DEBUG mode")
        }
    }

    /**
     * 记录连接时间到文件
     */
    fun logConnectionTime(record: ConnectionTimeRecord) {
        if (!BuildConfig.DEBUG || appContext == null) {
            return
        }

        try {
            val cacheDir = appContext!!.cacheDir
            val logFile = File(cacheDir, "connection_times.log")

            // 追加模式写入记录
            FileWriter(logFile, true).use { writer ->
                writer.write(formatRecord(record) + "\n")
            }

            logD(TAG, "连接时间已记录: ${record.deviceId}")
        } catch (e: Exception) {
            logD(TAG, "记录连接时间失败: ${e.message}")
        }
    }

    /**
     * 格式化连接记录
     */
    private fun formatRecord(record: ConnectionTimeRecord): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val timeStr = dateFormat.format(Date(record.startTime))
        val status = if (record.isSuccess) "成功" else "失败"
        val error = if (record.errorMessage != null) " [${record.errorMessage}]" else ""

        return "$timeStr | ${record.deviceId} | 蓝牙扫描:${record.bluetoothScanTime}ms | " +
                "蓝牙连接:${record.bluetoothConnectTime}ms | WiFi扫描:${record.wifiScanTime}ms | " +
                "WiFi连接:${record.wifiConnectTime}ms | Socket:${record.socketConnectTime}ms | " +
                "总时间:${record.totalTime}ms | $status$error"
    }

    /**
     * 清除日志文件
     */
    fun clearLogs() {
        if (!BuildConfig.DEBUG || appContext == null) {
            return
        }

        try {
            val cacheDir = appContext!!.cacheDir
            val logFile = File(cacheDir, "connection_times.log")

            if (logFile.exists()) {
                logFile.delete()
                logD(TAG, "连接时间日志已清除")
            }
        } catch (e: Exception) {
            logD(TAG, "清除日志失败: ${e.message}")
        }
    }
}


