package com.link.riderservice.feature.connection.wifi

import android.Manifest
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkInfo
import android.net.NetworkRequest
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.net.wifi.WifiNetworkSpecifier
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.link.riderservice.api.SPRiderServices
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.core.logging.logW
import com.link.riderservice.feature.analytics.ConnectionAnalytics
import com.link.riderservice.feature.connection.wifi.callback.IWiFiAPListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.net.Inet4Address

internal class WiFiAPManager(
    private val listener: IWiFiAPListener
) {
    companion object {
        private val TAG = WiFiAPManager::class.java.simpleName
    }

    private val context = SPRiderServices.getSharedInstance().application.applicationContext
    private val wifiManager: WifiManager
        get() = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
    private val connectivityManager: ConnectivityManager
        get() = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    private var currentState = WifiAPState.WIFI_AP_START
    private var targetSsid: String = ""
    private var targetPassword: String = ""
    private var currentNetwork: Network? = null
    private var reconnectionAttempts = 0
    private var connectionStartTime: Long = 0
    private var activeNetworkCallback: ConnectivityManager.NetworkCallback? = null
    private val connectionScope = CoroutineScope(Dispatchers.IO)

    private val wifiStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    handleWifiStateChanged(intent)
                }

                WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                    handleNetworkStateChanged(intent)
                }
            }
        }
    }

    init {
        logD(TAG, "WiFiAPManager initialized")
        val filter = IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
        }
        context.registerReceiver(wifiStateReceiver, filter)
    }

    fun connectToWifi(ssid: String, password: String) {
        logD(TAG, "Connecting to WiFi: $ssid")
        // 简单检查：如果正在连接中，直接返回
        if (currentState == WifiAPState.CONNECTING ||
            currentState == WifiAPState.WIFI_STATE_CHECK
        ) {
            logD(TAG, "Connection already in progress, current state: $currentState")
            return
        }

        // 检查是否已经连接到目标网络
        if (isActuallyConnectedToTarget(ssid)) {
            logD(TAG, "Already connected to target network: $ssid")
            val ipAddress = getCurrentNetworkIpAddress()
            if (ipAddress.isNotEmpty()) {
                updateState(WifiAPState.CONNECTION_VALIDATED)
                listener.onWifiConnected(ssid, ipAddress)
            }
            return
        }

        connectionStartTime = System.currentTimeMillis()
        // 记录WiFi连接开始
        ConnectionAnalytics.startWifiConnect()

        logD(TAG, "Attempting to connect: $ssid (AP Mode)")

        targetSsid = ssid
        targetPassword = password

        if (!checkPermissions()) {
            Log.e(TAG, "Missing required permissions")
            updateState(WifiAPState.PERMISSION_DENIED)
            listener.onWifiConnectionFailed(-2)
            return
        }

        if (!checkPermissions()) {
            logE(TAG, "Missing required permissions")
            updateState(WifiAPState.PERMISSION_DENIED)
            listener.onWifiConnectionFailed(-2)
            return
        }

        updateState(WifiAPState.WIFI_STATE_CHECK)
        checkWifiAndConnect(ssid, password)
    }

    /**
     * 检查WiFi状态并连接
     * 如果WiFi未启用，等待2秒后再次检查
     */
    private fun checkWifiAndConnect(ssid: String, password: String) {
        connectionScope.launch {
            try {
                if (wifiManager.isWifiEnabled) {
                    logD(TAG, "WiFi is enabled, proceeding with connection")
                    proceedWithConnection(ssid, password)
                    return@launch
                }

                logD(TAG, "WiFi is disabled, waiting 2s for retry")
                delay(2000)

                if (wifiManager.isWifiEnabled) {
                    logD(TAG, "WiFi is now enabled after 2s, proceeding with connection")
                    proceedWithConnection(ssid, password)
                } else {
                    logE(TAG, "WiFi is still disabled after 2s")
                    updateState(WifiAPState.WIFI_DISABLED)
                    listener.onWifiState(false)
                }
            } catch (e: Exception) {
                logE(TAG, "Error during WiFi check", e)
                updateState(WifiAPState.CONNECT_FAILED)
                listener.onWifiConnectionFailed(-3)
            }
        }
    }

    /**
     * 执行连接流程
     */
    private fun proceedWithConnection(ssid: String, password: String) {
        if (isActuallyConnectedToTarget(ssid)) {
            logD(TAG, "Already connected to target network with valid connection")
            val ipAddress = getCurrentNetworkIpAddress()
            if (ipAddress.isNotEmpty()) {
                updateState(WifiAPState.CONNECTION_VALIDATED)
                listener.onWifiConnected(ssid, ipAddress)
            }
            return
        }

        updateState(WifiAPState.CONNECTING)
        listener.onWifiConnecting(ssid)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            connectToWifiModern(ssid, password)
        }

    }

    fun disconnect() {
        logD(TAG, "Disconnecting WiFi")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            activeNetworkCallback?.let {
                try {
                    connectivityManager.unregisterNetworkCallback(it)
                } catch (e: Exception) {
                    logW(TAG, "Error during modern disconnect", e)
                }
                activeNetworkCallback = null
            }
        }
    }


    fun checkPermissions(): Boolean {
        val fineLocation = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val nearbyWifiDevices = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context, Manifest.permission.NEARBY_WIFI_DEVICES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        return fineLocation && nearbyWifiDevices
    }


    fun isWifiEnabled(): Boolean {
        return wifiManager.isWifiEnabled
    }

    fun getCurrentState(): WifiAPState {
        return currentState
    }

    fun getReconnectionAttempts(): Int {
        return reconnectionAttempts
    }

    // 移除getCurrentSessionId方法，不再需要

    fun resetConnectionState() {
        logD(TAG, "Resetting connection state")
        reconnectionAttempts = 0

        // 清理网络回调
        activeNetworkCallback?.let {
            try {
                connectivityManager.unregisterNetworkCallback(it)
            } catch (e: Exception) {
                logW(TAG, "Error unregistering network callback during reset", e)
            }
            activeNetworkCallback = null
        }

        // 重置网络相关状态
        currentNetwork = null
        targetSsid = ""
        targetPassword = ""

        when (currentState) {
            WifiAPState.CONNECTED,
            WifiAPState.CONNECTION_VALIDATED,
            WifiAPState.CONNECT_FAILED,
            WifiAPState.DISCONNECTED -> {
                updateState(WifiAPState.WIFI_AP_START)
            }

            else -> {
                logD(TAG, "Current state $currentState not reset")
            }
        }
    }

    fun destroy() {
        logD(TAG, "Destroying WiFiAPManager")
        try {
            context.unregisterReceiver(wifiStateReceiver)
        } catch (e: Exception) {
            logE(TAG, "Error unregistering receiver", e)
        }
        connectionScope.cancel()
        disconnect()
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun connectToWifiModern(ssid: String, password: String) {
        activeNetworkCallback?.let {
            try {
                connectivityManager.unregisterNetworkCallback(it)
            } catch (e: Exception) {
                logW(TAG, "Error during modern disconnect", e)
            }
            activeNetworkCallback = null
        }
        val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
            .setSsid(ssid)
            .setWpa2Passphrase(password)
            .build()

        val networkRequest = NetworkRequest.Builder()
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
            .removeCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .setNetworkSpecifier(wifiNetworkSpecifier)
            .build()

        val newCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                logD(TAG, "Network available: $network")
                currentNetwork = network
                val ipAddress = getNetworkIpAddress(network)
                updateState(WifiAPState.CONNECTED)
                if (validateConnection(ipAddress)) {
                    updateState(WifiAPState.CONNECTION_VALIDATED)
                    reconnectionAttempts = 0
                    // 记录WiFi连接完成，但不结束整个连接会话
                    ConnectionAnalytics.endWifiConnect()
                    logD(TAG, "WiFi连接成功, IP: $ipAddress, SSID: $targetSsid")
                    listener.onWifiConnected(targetSsid, ipAddress)
                } else {
                    updateState(WifiAPState.CONNECT_FAILED)
                    // 不在这里调用finishConnection，让onWifiConnectionFailed统一处理
                    logD(TAG, "WiFi连接验证失败: $targetSsid")
                    listener.onWifiConnectionFailed(-2)
                }
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                logD(TAG, "Wi-Fi lost: $targetSsid (AP Mode)")
                if (activeNetworkCallback == this) {
                    activeNetworkCallback = null
                }
                if (currentNetwork == network) {
                    currentNetwork = null
                    updateState(WifiAPState.DISCONNECTED)
                    listener.onWifiDisconnected()
                }
            }

            override fun onUnavailable() {
                super.onUnavailable()
                logD(TAG, "WiFi网络不可用: $targetSsid")
                updateState(WifiAPState.CONNECT_FAILED)
                listener.onWifiConnectionFailed(-1)
            }
        }

        try {
            connectivityManager.requestNetwork(networkRequest, newCallback)
            activeNetworkCallback = newCallback
            logD(TAG, "Modern WiFi connection request sent")
        } catch (e: Exception) {
            logD(TAG, "Modern API connection failed: ${e.localizedMessage}, SSID: $targetSsid (AP Mode)")
            updateState(WifiAPState.CONNECT_FAILED)
            listener.onWifiConnectionFailed(-3)
        }
    }

    @SuppressLint("MissingPermission")
    private fun getCurrentSsid(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            wifiInfo?.ssid?.replace("\"", "") ?: ""
        } catch (e: Exception) {
            logE(TAG, "Error getting current SSID", e)
            ""
        }
    }

    private fun getNetworkIpAddress(network: Network): String {
        return try {
            val linkProperties = connectivityManager.getLinkProperties(network)
            linkProperties?.linkAddresses?.find {
                it.address is Inet4Address
            }?.address?.hostAddress ?: ""
        } catch (e: Exception) {
            logE(TAG, "Error getting IP address", e)
            ""
        }
    }

    /**
     * 获取当前网络的IP地址（用于已连接状态检查）
     */
    @SuppressLint("MissingPermission")
    private fun getCurrentNetworkIpAddress(): String {
        return try {
            // 首先尝试从当前网络获取
            currentNetwork?.let { network ->
                val ipAddress = getNetworkIpAddress(network)
                if (ipAddress.isNotEmpty()) {
                    return ipAddress
                }
            }

            // 如果没有当前网络，尝试从活动网络获取
            val activeNetwork = connectivityManager.activeNetwork
            activeNetwork?.let { network ->
                val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                if (networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                    return getNetworkIpAddress(network)
                }
            }

            ""
        } catch (e: Exception) {
            logE(TAG, "Error getting current network IP address", e)
            ""
        }
    }

    @SuppressLint("MissingPermission")
    private fun isActuallyConnectedToTarget(targetSsid: String): Boolean {
        try {
            val currentSsid = getCurrentSsid()
            if (currentSsid != targetSsid) {
                logD(TAG, "SSID mismatch: current=$currentSsid, target=$targetSsid")
                return false
            }

            val wifiInfo = wifiManager.connectionInfo
            if (wifiInfo == null || wifiInfo.networkId == -1) {
                logD(TAG, "WiFi not connected or invalid network ID")
                return false
            }

            val activeNetwork = connectivityManager.activeNetwork
            if (activeNetwork == null) {
                logD(TAG, "No active network")
                return false
            }

            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
            if (networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) != true) {
                logD(TAG, "Active network is not WiFi")
                return false
            }

            val ipAddress = getCurrentNetworkIpAddress()
            if (ipAddress.isEmpty() || !validateConnection(ipAddress)) {
                logD(TAG, "Invalid or empty IP address: $ipAddress")
                return false
            }

            currentNetwork = activeNetwork
            if (currentState != WifiAPState.CONNECTION_VALIDATED) {
                updateState(WifiAPState.CONNECTION_VALIDATED)
            }

            logD(TAG, "Actually connected to target network: $targetSsid, IP: $ipAddress")
            return true

        } catch (e: Exception) {
            logE(TAG, "Error checking actual connection status", e)
            return false
        }
    }

    private fun validateConnection(ipAddress: String): Boolean {
        if (ipAddress.isEmpty()) {
            logW(TAG, "IP address is empty")
            return false
        }

        try {
            val parts = ipAddress.split(".")
            if (parts.size != 4) return false
            parts.forEach {
                val num = it.toInt()
                if (num < 0 || num > 255) return false
            }
        } catch (e: Exception) {
            logE(TAG, "Invalid IP address format: $ipAddress", e)
            return false
        }

        return true
    }


    private fun updateState(newState: WifiAPState) {
        logD(TAG, "State changed: $currentState -> $newState")
        currentState = newState
    }

    private fun reConnect() {
        if (targetSsid.isNotEmpty() &&
            currentState != WifiAPState.CONNECTED &&
            currentState != WifiAPState.CONNECTING &&
            currentState != WifiAPState.CONNECTION_VALIDATED &&
            currentState != WifiAPState.WIFI_STATE_CHECK
        ) {
            logD(TAG, "WiFi enabled, requesting WiFi info for reconnection")
            listener.requestWifiInfo()
        } else {
            logD(TAG, "Skipping reconnection: targetSsid=$targetSsid, currentState=$currentState")
        }
    }

    private fun handleWifiStateChanged(intent: Intent) {
        val state = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
        when (state) {
            WifiManager.WIFI_STATE_DISABLED -> {
                logD(TAG, "WiFi disabled")
                listener.onWifiState(false)
                updateState(WifiAPState.WIFI_DISABLED)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    activeNetworkCallback?.let {
                        try {
                            connectivityManager.unregisterNetworkCallback(it)
                        } catch (e: Exception) {
                            logE(TAG, "Error unregistering network callback", e)
                        }
                        activeNetworkCallback = null
                    }
                }
            }

            WifiManager.WIFI_STATE_ENABLED -> {
                logD(TAG, "WiFi enabled")
                listener.onWifiState(true)
                reConnect()
            }
        }
    }

    private fun handleNetworkStateChanged(intent: Intent) {
        val networkInfo = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
        if (networkInfo == null) {
            logW(TAG, "Network state changed: networkInfo is null")
            return
        }

        if (networkInfo.type != ConnectivityManager.TYPE_WIFI) {
            return
        }
        var wifiInfo = intent.getParcelableExtra<WifiInfo>(WifiManager.EXTRA_WIFI_INFO)
        if (wifiInfo == null) {
            wifiInfo = wifiManager.connectionInfo
        }
        if (wifiInfo == null) {
            logE(TAG, "Network state changed: wifiInfo is null")
            return
        }
        logD(TAG, "Network state changed: ${networkInfo.state} - ssid: ${wifiInfo.ssid}")
    }
}

/**
 * WiFi客户端连接状态
 */
enum class WifiAPState {
    WIFI_AP_START,      // 初始化状态
    PERMISSION_DENIED,      // 权限被拒绝
    WIFI_STATE_CHECK,       // 检查WiFi状态
    WIFI_DISABLED,          // WiFi未开启
    CONNECTING,             // 连接中
    CONNECTED,              // 已连接
    CONNECTION_VALIDATED,   // 连接验证通过
    DISCONNECTED,           // 已断开
    CONNECT_FAILED          // 连接失败
} 