package com.link.riderservice.feature.cast.video

import kotlin.math.roundToInt

/**
 * 显示/分辨率相关计算工具。
 */
internal object DisplayUtils {
    private const val BASE_DPI = 160.0f
    private const val DPI_MULTIPLIER = 1.6f
    private const val ASPECT_RATIO_THRESHOLD_MIN = 1.34f
    private const val ASPECT_RATIO_THRESHOLD_MAX = 2.0f

    private val RESOLUTION_DPI_MAP: Map<Pair<Int, Int>, Int> = run {
        val m = HashMap<Pair<Int, Int>, Int>()
        m[1024 to 600] = 256
        m[1024 to 496] = 256
        m[1280 to 720] = 320
        m[1920 to 1080] = 480
        m[800 to 480] = 240
        m
    }

    /**
     * 根据宽高计算 densityDpi。
     */
    fun calculateDensityDpi(width: Int, height: Int): Int {
        val maxDimension = maxOf(width, height)
        val minDimension = minOf(width, height)

        val preset = RESOLUTION_DPI_MAP[maxDimension to minDimension]
        if (preset != null) return preset

        val aspectRatio = maxDimension / minDimension.toFloat()
        return when {
            aspectRatio < ASPECT_RATIO_THRESHOLD_MIN -> 256
            aspectRatio > ASPECT_RATIO_THRESHOLD_MAX ->
                (((minDimension * BASE_DPI) / 640.0f) * DPI_MULTIPLIER).roundToInt()
            else ->
                (((minDimension * BASE_DPI) / 720.0f) * DPI_MULTIPLIER).roundToInt()
        }
    }
}


