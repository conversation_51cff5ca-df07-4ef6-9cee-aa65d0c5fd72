package com.link.riderservice.feature.analytics

/**
 * 简化的连接时间记录
 */
internal data class ConnectionTimeRecord(
    val sessionId: String,
    val deviceId: String,
    val startTime: Long,
    var bluetoothScanTime: Long = 0,
    var bluetoothConnectTime: Long = 0,
    var wifiScanTime: Long = 0,
    var wifiConnectTime: Long = 0,
    var socketConnectTime: Long = 0,
    var totalTime: Long = 0,
    var isSuccess: Boolean = false,
    var errorMessage: String? = null
) {
    /**
     * 获取总耗时
     */
    fun getTotalDuration(): Long = System.currentTimeMillis() - startTime

    /**
     * 格式化输出
     */
    override fun toString(): String {
        return "连接记录[$deviceId]: 蓝牙扫描=${bluetoothScanTime}ms, 蓝牙连接=${bluetoothConnectTime}ms, " +
                "WiFi扫描=${wifiScanTime}ms, WiFi连接=${wifiConnectTime}ms, " +
                "Socket连接=${socketConnectTime}ms, 总时间=${totalTime}ms, 成功=$isSuccess"
    }
}
