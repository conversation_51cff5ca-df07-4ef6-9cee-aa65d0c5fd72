package com.link.riderservice.feature.connection.ble.callback

import android.bluetooth.BluetoothDevice
import com.link.riderservice.feature.connection.ble.model.BleDevice
import com.link.riderservice.libs.ble.data.Data

internal interface RiderBleCallback {
    fun onDataReceived(device: BluetoothDevice, receivedData: Data)
    fun onDeviceConnecting(device: BluetoothDevice)
    fun onDeviceConnected(device: BluetoothDevice)
    fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int)
    fun onDeviceReady(device: BluetoothDevice)
    fun onDeviceDisconnecting(device: BluetoothDevice)
    fun onDeviceDisconnected(device: BluetoothDevice, reason: Int)
    fun onScanResult(devices: List<BleDevice>)
    fun onRequestBt()
    fun onScanning()
    fun onScanFinish()
    fun onNeedBluetoothScanPermission()
    fun onNeedLocationPermission()
    fun onEnableNotificationFailed(device: BluetoothDevice, status: Int)
}