package com.link.riderservice.feature.connection.transport.tcp

/**
 * TCP连接配置类
 * 封装TCP连接的各种配置参数，支持灵活配置
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */
internal data class TcpConnectionConfig(
    /**
     * 监听端口
     */
    val listenPort: Int = TcpConnectionConstants.DEFAULT_LISTEN_PORT,
    
    /**
     * 连接超时时间（毫秒）
     */
    val connectionTimeout: Int = TcpConnectionConstants.CONNECTION_TIMEOUT,
    
    /**
     * Socket读取超时时间（毫秒）
     */
    val soTimeout: Int = TcpConnectionConstants.SO_TIMEOUT,
    
    /**
     * 接受连接超时时间（毫秒）
     */
    val acceptTimeout: Int = TcpConnectionConstants.ACCEPT_TIMEOUT,
    
    /**
     * 最大重试次数
     */
    val maxRetryCount: Int = TcpConnectionConstants.MAX_RETRY_COUNT,
    
    /**
     * 重试延迟时间（毫秒）
     */
    val retryDelay: Long = TcpConnectionConstants.RETRY_DELAY,
    
    /**
     * 是否启用TCP_NODELAY
     */
    val tcpNoDelay: Boolean = TcpConnectionConstants.TCP_NO_DELAY,
    
    /**
     * 是否启用Keep-Alive
     */
    val keepAlive: Boolean = TcpConnectionConstants.KEEP_ALIVE,
    
    /**
     * 是否重用地址
     */
    val reuseAddress: Boolean = TcpConnectionConstants.REUSE_ADDRESS,
    
    /**
     * 缓冲区大小
     */
    val bufferSize: Int = TcpConnectionConstants.DEFAULT_BUFFER_SIZE,
    
    /**
     * 是否启用详细日志
     */
    val enableVerboseLogging: Boolean = false,

    /**
     * 是否启用IP地址发送（通过BLE发送给仪表端）
     */
    val enableIpAddressSending: Boolean = true
) {
    
    companion object {
        /**
         * 创建默认配置
         */
        fun createDefault(): TcpConnectionConfig {
            return TcpConnectionConfig()
        }
        
        /**
         * 创建服务器模式配置
         */
        fun createServerConfig(
            port: Int = TcpConnectionConstants.DEFAULT_LISTEN_PORT,
            enableVerboseLogging: Boolean = false,
            enableIpAddressSending: Boolean = true
        ): TcpConnectionConfig {
            return TcpConnectionConfig(
                listenPort = port,
                enableVerboseLogging = enableVerboseLogging,
                enableIpAddressSending = enableIpAddressSending
            )
        }
        
        /**
         * 创建客户端模式配置
         */
        fun createClientConfig(
            connectionTimeout: Int = TcpConnectionConstants.CONNECTION_TIMEOUT,
            enableVerboseLogging: Boolean = false
        ): TcpConnectionConfig {
            return TcpConnectionConfig(
                connectionTimeout = connectionTimeout,
                enableVerboseLogging = enableVerboseLogging
            )
        }
        
        /**
         * 创建高性能配置
         */
        fun createHighPerformanceConfig(): TcpConnectionConfig {
            return TcpConnectionConfig(
                bufferSize = TcpConnectionConstants.MAX_BUFFER_SIZE,
                tcpNoDelay = true,
                keepAlive = true
            )
        }
    }
    
    /**
     * 验证配置参数的有效性
     */
    fun validate(): Boolean {
        return listenPort in 1024..65535 &&
               connectionTimeout > 0 &&
               soTimeout > 0 &&
               acceptTimeout > 0 &&
               maxRetryCount >= 0 &&
               retryDelay >= 0 &&
               bufferSize > 0
    }
}
