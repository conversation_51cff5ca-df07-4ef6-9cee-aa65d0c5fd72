package com.link.riderservice.feature.connection.transport.tcp

import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.feature.connection.transport.DataConnection
import java.io.IOException
import java.net.BindException
import java.net.Inet4Address
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.NetworkInterface
import java.net.ServerSocket
import java.net.Socket
import java.net.SocketTimeoutException
import java.util.Enumeration
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 在AP模式下：
 * - 手机端作为TCP服务器（监听端口30512）
 * - 仪表端作为TCP客户端连接
 * - 通过BLE发送手机IP地址给仪表端
 */
internal class TcpServerConnection(
    private val phoneIpAddress: String
) : DataConnection() {
    private var serverSocket: ServerSocket? = null
    private var clientSocket: Socket? = null
    private var isServerRunning = AtomicBoolean(false)
    private var acceptThread: AcceptThread? = null

    companion object {
        private const val TAG = "TcpServerConnection"
        const val LISTEN_PORT = 30512
        private const val CONNECTION_TIMEOUT = 20000 // 20秒连接超时
        private const val SO_TIMEOUT = 20000 // 20秒读取超时
        private const val MAX_RETRY_COUNT = 3
    }

    /**
     * 启动TCP服务器
     */
    @Throws(IOException::class)
    override fun onStart() {
        if (isServerRunning.get()) {
            logD(TAG, "TCP server already running")
            return
        }

        try {
            startTcpServer()

            // 使用构造函数传入的IP地址或获取手机IP地址
            val phoneIp = phoneIpAddress.takeIf { it.isNotEmpty() } ?: getPhoneIpAddress()
            if (phoneIp.isNotEmpty()) {

                // 启动接受连接的线程
                acceptThread = AcceptThread("TCP Server Accept")
                acceptThread?.start()
            } else {
                logE(TAG, "Failed to get phone IP address")
                throw IOException("Unable to get phone IP address")
            }

        } catch (e: IOException) {
            logE(TAG, "Failed to start TCP server", e)
            isServerRunning.set(false)
            throw e
        }
    }

    private fun startTcpServer() {
        if (serverSocket == null || serverSocket?.isClosed == true) {
            serverSocket = ServerSocket().apply {
                reuseAddress = true
                soTimeout = CONNECTION_TIMEOUT
            }

            try {
                serverSocket?.bind(InetSocketAddress(phoneIpAddress, LISTEN_PORT))
                isServerRunning.set(true)
                logD(TAG, "TCP server started on port $LISTEN_PORT")
            } catch (e: BindException) {
                logE(TAG, "Failed to bind to port $LISTEN_PORT: ${e.message}")
                throw IOException("Port $LISTEN_PORT is already in use", e)
            }
        }
    }

    /**
     * 获取手机在WiFi网络中的IP地址
     */
    private fun getPhoneIpAddress(): String {
        try {
            val networkInterfaces: Enumeration<NetworkInterface> =
                NetworkInterface.getNetworkInterfaces()
            while (networkInterfaces.hasMoreElements()) {
                val networkInterface: NetworkInterface = networkInterfaces.nextElement()

                // 跳过回环和未启用的接口
                if (networkInterface.isLoopback || !networkInterface.isUp) {
                    continue
                }

                val inetAddresses: Enumeration<InetAddress> = networkInterface.inetAddresses
                while (inetAddresses.hasMoreElements()) {
                    val inetAddress: InetAddress = inetAddresses.nextElement()

                    // 只处理IPv4地址
                    if (inetAddress is Inet4Address && !inetAddress.isLoopbackAddress) {
                        val interfaceName = networkInterface.name.lowercase()

                        // 对于AP客户端模式，查找WiFi接口
                        if (interfaceName.contains("wlan") ||
                            interfaceName.contains("wifi") ||
                            interfaceName.startsWith("eth")
                        ) {

                            val ipAddress = inetAddress.hostAddress
                            logD(
                                TAG,
                                "Found WiFi IP address: $ipAddress on interface: ${networkInterface.name}"
                            )
                            return ipAddress ?: ""
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logE(TAG, "Error getting phone IP address", e)
        }

        logE(TAG, "No suitable WiFi IP address found")
        return ""
    }

    /**
     * 接受TCP客户端连接的线程
     */
    inner class AcceptThread(name: String) : Thread(name) {
        override fun run() {
            var retryCount = 0

            while (isServerRunning.get() && retryCount < MAX_RETRY_COUNT) {
                try {
                    logD(
                        TAG,
                        "Waiting for instrument TCP client connection... (attempt ${retryCount + 1})"
                    )
                    val incomingSocket = serverSocket?.accept()
                    if (incomingSocket != null) {
                        logD(
                            TAG,
                            "Instrument connected from: ${incomingSocket.remoteSocketAddress}"
                        )

                        // 配置socket参数
                        incomingSocket.tcpNoDelay = true
                        incomingSocket.soTimeout = SO_TIMEOUT
                        incomingSocket.keepAlive = true

                        clientSocket = incomingSocket

                        // 创建传输层
                        val transport = TcpTransport(incomingSocket)
                        eventCallback?.onConnected(transport)

                        // 连接成功，退出重试循环
                        break

                    } else {
                        logE(TAG, "Failed to accept connection - socket is null")
                        retryCount++
                    }

                } catch (e: SocketTimeoutException) {
                    logW(
                        TAG,
                        "Connection timeout, retrying... (${retryCount + 1}/$MAX_RETRY_COUNT)"
                    )
                    retryCount++

                } catch (e: IOException) {
                    if (isServerRunning.get()) {
                        logE(TAG, "Error accepting TCP connection", e)
                        retryCount++
                    } else {
                        logD(TAG, "Server socket closed normally")
                        break
                    }
                } catch (e: Exception) {
                    logE(TAG, "Unexpected error in accept thread", e)
                    retryCount++
                }
            }

            // 如果重试次数用完，通知连接失败
            if (retryCount >= MAX_RETRY_COUNT && isServerRunning.get()) {
                logE(TAG, "Max retry attempts reached, connection failed")
                eventCallback?.onDisconnected()
            }
        }
    }

    override fun onShutdown() {
        logD(TAG, "Shutting down TCP server")

        isServerRunning.set(false)

        // 停止接受线程
        acceptThread?.interrupt()
        acceptThread = null

        // 关闭客户端连接
        try {
            clientSocket?.close()
        } catch (e: IOException) {
            logE(TAG, "Error closing client socket", e)
        }
        clientSocket = null

        // 关闭服务器socket
        try {
            serverSocket?.close()
        } catch (e: IOException) {
            logE(TAG, "Error closing server socket", e)
        }
        serverSocket = null

        logD(TAG, "TCP server shutdown completed")
    }

    /**
     * 检查服务器是否运行
     */
    fun isServerRunning(): Boolean {
        return isServerRunning.get() && serverSocket?.isClosed == false
    }

    /**
     * 检查是否有客户端连接
     */
    fun hasClientConnected(): Boolean {
        return clientSocket?.isConnected == true && clientSocket?.isClosed == false
    }

    /**
     * 获取连接信息
     */
    fun getConnectionInfo(): String {
        return when {
            !isServerRunning() -> "Server not running"
            !hasClientConnected() -> "Server running on port $LISTEN_PORT, waiting for client"
            else -> "Client connected from ${clientSocket?.remoteSocketAddress}"
        }
    }

    /**
     * 日志工具方法
     */
    private fun logW(tag: String, message: String) {
        logD(tag, "WARNING: $message")
    }
}
