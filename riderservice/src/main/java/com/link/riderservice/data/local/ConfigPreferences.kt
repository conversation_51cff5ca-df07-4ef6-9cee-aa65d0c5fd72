package com.link.riderservice.data.local

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit

internal class ConfigPreferences private constructor(context: Context) {

    companion object {
        private const val PREF_NAME = "config_auto_connection"
        private const val KEY_BLE_NAME = "ble_name"
        private const val KEY_BLE_ADDRESS = "ble_address"
        private const val KEY_WIFI_ADDRESS = "wifi_address"
        private const val KEY_WIFI_PORT = "wifi_port"
        private const val KEY_SCAN_BLE_ADDRESS = "scan_ble_address"
        private const val KEY_AP_SSID = "ap_ssid"
        private const val KEY_AP_PASSWORD = "ap_password"

        @Volatile
        private var INSTANCE: ConfigPreferences? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): ConfigPreferences {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ConfigPreferences(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

    fun setBleName(name: String) {
        sharedPreferences.edit {
            putString(KEY_BLE_NAME, name)
        }
    }


    fun getBleName(): String? {
        return sharedPreferences.getString(KEY_BLE_NAME, null)
    }


    fun setBleAddress(address: String) {
        sharedPreferences.edit {
            putString(KEY_BLE_ADDRESS, address)
        }
    }

    fun getBleAddress(): String? {
        return sharedPreferences.getString(KEY_BLE_ADDRESS, null)
    }


    fun setWifiAddress(address: String) {
        sharedPreferences.edit {
            putString(KEY_WIFI_ADDRESS, address)
        }
    }


    fun getWifiAddress(): String? {
        return sharedPreferences.getString(KEY_WIFI_ADDRESS, null)
    }


    fun setWifiPort(port: Int) {
        sharedPreferences.edit {
            putInt(KEY_WIFI_PORT, port)
        }
    }


    fun getWifiPort(): Int {
        return sharedPreferences.getInt(KEY_WIFI_PORT, 0)
    }


    fun setScanBleAddress(address: String) {
        sharedPreferences.edit {
            putString(KEY_SCAN_BLE_ADDRESS, address)
        }
    }


    fun getScanBleAddress(): String? {
        return sharedPreferences.getString(KEY_SCAN_BLE_ADDRESS, null)
    }


    fun contains(key: String): Boolean {
        return sharedPreferences.contains(key)
    }


    fun containsScanBleAddress(): Boolean {
        return sharedPreferences.contains(KEY_SCAN_BLE_ADDRESS)
    }


    fun containsBleAddress(): Boolean {
        return sharedPreferences.contains(KEY_BLE_ADDRESS)
    }


    fun removeScanBleAddress() {
        sharedPreferences.edit {
            remove(KEY_SCAN_BLE_ADDRESS)
        }
    }


    fun setApSsid(ssid: String) {
        sharedPreferences.edit {
            putString(KEY_AP_SSID, ssid)
        }
    }


    fun getApSsid(): String? {
        return sharedPreferences.getString(KEY_AP_SSID, null)
    }


    fun setApPassword(password: String) {
        sharedPreferences.edit {
            putString(KEY_AP_PASSWORD, password)
        }
    }


    fun getApPassword(): String? {
        return sharedPreferences.getString(KEY_AP_PASSWORD, null)
    }


    fun clearAllConfig() {
        sharedPreferences.edit {
            remove(KEY_BLE_NAME)
            remove(KEY_BLE_ADDRESS)
            remove(KEY_WIFI_ADDRESS)
            remove(KEY_WIFI_PORT)
            remove(KEY_AP_SSID)
            remove(KEY_AP_PASSWORD)
        }
    }

     fun getSharedPreferences(): SharedPreferences {
        return sharedPreferences
    }
}