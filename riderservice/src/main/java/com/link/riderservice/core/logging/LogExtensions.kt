package com.link.riderservice.core.logging

/**
 * 日志扩展函数
 * 提供便捷的日志记录方法
 */

// ==================== 全局日志函数 ====================

/**
 * 记录详细信息
 */
fun logV(tag: String, message: String) {
    LogManager.v(tag, message)
}

/**
 * 记录详细信息（带异常）
 */
fun logV(tag: String, message: String, throwable: Throwable?) {
    LogManager.v(tag, message, throwable)
}

/**
 * 记录调试信息
 */
fun logD(tag: String, message: String) {
    LogManager.d(tag, message)
}

/**
 * 记录调试信息（带异常）
 */
fun logD(tag: String, message: String, throwable: Throwable?) {
    LogManager.d(tag, message, throwable)
}

/**
 * 记录一般信息
 */
fun logI(tag: String, message: String) {
    LogManager.i(tag, message)
}

/**
 * 记录一般信息（带异常）
 */
fun logI(tag: String, message: String, throwable: Throwable?) {
    LogManager.i(tag, message, throwable)
}

/**
 * 记录警告信息
 */
fun logW(tag: String, message: String) {
    LogManager.w(tag, message)
}

/**
 * 记录警告信息（带异常）
 */
fun logW(tag: String, message: String, throwable: Throwable?) {
    LogManager.w(tag, message, throwable)
}

/**
 * 记录错误信息
 */
fun logE(tag: String, message: String) {
    LogManager.e(tag, message)
}

/**
 * 记录错误信息（带异常）
 */
fun logE(tag: String, message: String, throwable: Throwable?) {
    LogManager.e(tag, message, throwable)
}

// ==================== Any类的扩展函数 ====================

/**
 * 为Any类添加日志扩展方法，使用类名作为TAG
 */
private val Any.logTag: String
    get() = this::class.java.simpleName

/**
 * 记录详细信息（使用类名作为TAG）
 */
fun Any.logV(message: String) {
    LogManager.v(logTag, message)
}

/**
 * 记录详细信息（使用类名作为TAG，带异常）
 */
fun Any.logV(message: String, throwable: Throwable?) {
    LogManager.v(logTag, message, throwable)
}

/**
 * 记录调试信息（使用类名作为TAG）
 */
fun Any.logD(message: String) {
    LogManager.d(logTag, message)
}

/**
 * 记录调试信息（使用类名作为TAG，带异常）
 */
fun Any.logD(message: String, throwable: Throwable?) {
    LogManager.d(logTag, message, throwable)
}

/**
 * 记录一般信息（使用类名作为TAG）
 */
fun Any.logI(message: String) {
    LogManager.i(logTag, message)
}

/**
 * 记录一般信息（使用类名作为TAG，带异常）
 */
fun Any.logI(message: String, throwable: Throwable?) {
    LogManager.i(logTag, message, throwable)
}

/**
 * 记录警告信息（使用类名作为TAG）
 */
fun Any.logW(message: String) {
    LogManager.w(logTag, message)
}

/**
 * 记录警告信息（使用类名作为TAG，带异常）
 */
fun Any.logW(message: String, throwable: Throwable?) {
    LogManager.w(logTag, message, throwable)
}

/**
 * 记录错误信息（使用类名作为TAG）
 */
fun Any.logE(message: String) {
    LogManager.e(logTag, message)
}

/**
 * 记录错误信息（使用类名作为TAG，带异常）
 */
fun Any.logE(message: String, throwable: Throwable?) {
    LogManager.e(logTag, message, throwable)
} 