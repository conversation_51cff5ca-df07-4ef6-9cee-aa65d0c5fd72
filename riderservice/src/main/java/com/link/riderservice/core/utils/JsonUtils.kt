package com.link.riderservice.core.utils

import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import kotlin.reflect.KMutableProperty0


internal class JSON {
    var jsonObject: JSONObject = JSONObject()

    constructor(jsonObject: JSONObject) {
        this.jsonObject = jsonObject
    }

    constructor(jsonString: String?) {
        try {
            this.jsonObject = JSONObject(jsonString)
        } catch (e: JSONException) {
            this.jsonObject = JSONObject()
        }
    }
}

// Container to store infos in DSL.
internal data class JSONMappingInfo<T>(val json: JSON, val key: String, var parser: JSONParser<T>? = null)

// "[]" overloading returning JSONMappingInfo<T> containing all the data needed for the parsing.
// Enables writing json["key"] or json["key", MyJSONParser()]
internal operator fun <T> JSON.get(key: String, parser: JSONParser<T>? = null): JSONMappingInfo<T> =
    JSONMappingInfo(this, key, parser)

// An interface to create your own JSONParser objects, that can then be used while parsing.
internal interface JSONParser<Model> {
    @Throws(JSONException::class)
    fun parse(json: JSON): Model
}

// Adds support for "<" operator for parsing.
internal inline operator fun <reified T> KMutableProperty0<T>.compareTo(mapping: JSONMappingInfo<T>): Int {
    mapJSON(this, mapping)
    return 0
}

internal inline fun <reified T> mapJSON(property: KMutableProperty0<T>, json: JSON, key: String) {
    getValue<T>(json, key)?.let { property.set(it) }
}

// Mapping with a custom parser.
internal inline fun <reified T> mapJSON(
    property: KMutableProperty0<T>,
    json: JSON,
    key: String,
    parser: JSONParser<T>
) {
    json.jsonOrNull(key)?.let { subJSON ->
        val model = parser.parse(subJSON)
        property.set(model)
    }
}

internal inline fun <reified T> mapJSON(property: KMutableProperty0<T>, mapping: JSONMappingInfo<T>) {
    val json = mapping.json
    val key = mapping.key
    val parser = mapping.parser
    if (parser != null) {
        mapJSON(property, json, key, parser)
    } else {
        mapJSON(property, json, key)
    }
}

// Null getters with json("id")
internal inline operator fun <reified T> JSON.invoke(key: String): T? {
    return getValue(this, key)
}


// Null getters with json("id")
internal inline fun <reified T> getValue(json: JSON, key: String): T? {
    if (isKeyPath(key)) {
        val keys = key.split(".")
        val lastKey = keys.last()
        val allKeysButLast = keys.dropLast(1)
        var nestedJSON = json
        allKeysButLast.forEach { k ->
            if (!nestedJSON.jsonObject.has(k)) {
                return null
            }
            nestedJSON = JSON(nestedJSON.jsonObject.getJSONObject(k))
        }
        return getSingleValue(nestedJSON, lastKey)
    }
    return getSingleValue(json, key)
}


internal inline fun <reified T> getSingleValue(json: JSON, key: String): T? {
    if (key.isBlank() || json.jsonObject.length() < 1 || !json.jsonObject.has(key)) {
        return null
    }
    return when (T::class) {
        Boolean::class -> json.bool(key) as? T
        String::class -> json.string(key) as? T
        Int::class -> json.int(key) as? T
        Long::class -> json.long(key) as? T
        Double::class -> json.double(key) as? T
        JSONObject::class -> json.jsonObject.getJSONObject(key) as? T
        JSON::class -> json.jsonObject.getJSONObject(key)?.let { JSON(it) } as? T
        JSONArray::class -> json.jsonObject.getJSONArray(key) as? T
        else -> null
    }
}


internal inline operator fun <reified T> JSON.invoke(key: String, parser: JSONParser<T>): T? {
    return jsonOrNull(key)?.let { return parser.parse(it) }
}

// Helpers
internal fun JSON.bool(key: String): Boolean = jsonObject.getBoolean(key)
internal fun JSON.string(key: String): String = jsonObject.getString(key)
internal fun JSON.int(key: String): Int = jsonObject.getInt(key)
internal fun JSON.long(key: String): Long = jsonObject.getLong(key)
internal fun JSON.double(key: String): Double = jsonObject.getDouble(key)

// Optional Helpers
internal fun JSON.boolOrNull(key: String): Boolean? =
    if (jsonObject.has(key)) jsonObject.getBoolean(key) else null

internal fun JSON.intOrNull(key: String): Int? = if (jsonObject.has(key)) jsonObject.getInt(key) else null
internal fun JSON.doubleOrNull(key: String): Double? =
    if (jsonObject.has(key)) jsonObject.getDouble(key) else null

internal fun JSON.longOrNull(key: String): Long? = if (jsonObject.has(key)) jsonObject.getLong(key) else null
internal fun JSON.stringOrNull(key: String): String? =
    if (jsonObject.has(key)) jsonObject.getString(key) else null

internal fun JSON.jsonOrNull(key: String): JSON? {
    if (jsonObject.has(key)) {
        return try {
            JSON(jsonObject.getJSONObject(key))
        } catch (e: JSONException) {
            e
            null
        }
    }
    return null
}


internal fun isKeyPath(key: String) = key.contains(".")
