package com.link.riderservice.core.utils

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.content.pm.PackageManager
import android.location.LocationManager
import android.os.Build
import android.os.ParcelUuid
import androidx.annotation.ChecksSdkIntAtLeast
import androidx.core.content.ContextCompat
import androidx.core.location.LocationManagerCompat
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.libs.ble.scanner.ScanResult

internal object BleUtils {
    private const val TAG = "BleUtils"
    private val EDDYSTONE_UUID = ParcelUuid.fromString("0000FEAA-0000-1000-8000-00805f9b34fb")
    private const val COMPANY_ID_MICROSOFT = 0x0006
    private const val COMPANY_ID_APPLE = 0x004C
    private const val COMPANY_ID_NORDIC_SEMI = 0x0059

    fun isBleEnabled(): Boolean {
        val adapter = BluetoothAdapter.getDefaultAdapter()
        return adapter != null && adapter.isEnabled
    }

    @SuppressWarnings("MissingPermission")
    fun enableBluetooth(): Boolean {
        val adapter = BluetoothAdapter.getDefaultAdapter()
        if (adapter != null && !adapter.isEnabled) {
            return adapter.enable()
        }
        return true
    }

    fun isLocationEnabled(context: Context): Boolean {
        val lm = context.getSystemService(LocationManager::class.java)
        return LocationManagerCompat.isLocationEnabled(lm)
    }

    fun isLocationPermissionGranted(context: Context): Boolean {
        return (ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION)
                == PackageManager.PERMISSION_GRANTED)
    }

    fun isBluetoothScanPermissionGranted(context: Context): Boolean {
        return if (!isSorAbove()) true else ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.BLUETOOTH_SCAN
        ) == PackageManager.PERMISSION_GRANTED
    }

    fun isBluetoothConnectPermissionGranted(context: Context): Boolean {
        return if (!isSorAbove()) true else ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.BLUETOOTH_CONNECT
        ) == PackageManager.PERMISSION_GRANTED
    }

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.S)
    fun isSorAbove(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    }

    fun isBeacon(result: ScanResult): Boolean {
        if (result.scanRecord != null) {
            val record = result.scanRecord
            val appleData =
                record!!.getManufacturerSpecificData(COMPANY_ID_APPLE)
            if (appleData != null) {
                // iBeacons
                if (appleData.size == 23 && appleData[0] == (0x02).toByte() && appleData[1] == (0x15).toByte()) return true
            }
            val nordicData =
                record.getManufacturerSpecificData(COMPANY_ID_NORDIC_SEMI)
            if (nordicData != null) {
                // Nordic Beacons
                if (nordicData.size == 23 && nordicData[0] == (0x02).toByte() && nordicData[1] == (0x15).toByte()) return true
            }
            val microsoftData =
                record.getManufacturerSpecificData(COMPANY_ID_MICROSOFT)
            if (microsoftData != null) {
                // Microsoft Advertising Beacon
                if (microsoftData[0] == (0x01).toByte()) // Scenario Type = Advertising Beacon
                    return true
            }

            // Eddystone
            val eddystoneData =
                record.getServiceData(EDDYSTONE_UUID)
            if (eddystoneData != null) return true
        }
        return false
    }

    fun isAirDrop(result: ScanResult): Boolean {
        if (result.scanRecord != null) {
            val record = result.scanRecord

            // iPhones and iMacs advertise with AirDrop packets
            val appleData =
                record!!.getManufacturerSpecificData(COMPANY_ID_APPLE)
            return appleData != null && appleData.size > 1 && appleData[0] == (0x10).toByte()
        }
        return false
    }

    fun isTarget(result: ScanResult): Boolean {
        if (result.scanRecord != null) {
            val address = result.device.address
            val record = result.scanRecord
            if (address.replace(":", "") == record?.deviceName) {
                return true
            }
        }
        return false
    }

    /**
     * 解析二维码内容并提取蓝牙MAC地址
     * @param qrCodeValue 二维码字符串内容
     * @return 格式化后的MAC地址，解析失败返回null
     */
    fun parseQRCodeForMacAddress(qrCodeValue: String): String? {
        try {
            logD(TAG, "Parsing QR code value for MAC address: $qrCodeValue")

            // 解析格式：BtMacAddress=597B004A0F53;WifiName=Geminic340dc;WifiPassword=88888888;WifiSecurity=wpa2-psk
            val pairs = qrCodeValue.split(";")

            for (pair in pairs) {
                val keyValue = pair.split("=", limit = 2)
                if (keyValue.size == 2 && keyValue[0].trim() == "BtMacAddress") {
                    val macAddress = keyValue[1].trim()
                    // 格式化MAC地址（添加冒号分隔符）
                    return formatMacAddress(macAddress)
                }
            }

            logE(TAG, "BtMacAddress not found in QR code")
            return null

        } catch (e: Exception) {
            logE(TAG, "Failed to parse QR code value: $qrCodeValue", e)
            return null
        }
    }

    /**
     * 解析二维码内容并提取完整信息
     * @param qrCodeValue 二维码字符串内容
     * @return 解析后的二维码信息，解析失败返回null
     */
    fun parseQRCodeInfo(qrCodeValue: String): QRCodeInfo? {
        try {
            logD(TAG, "Parsing QR code value: $qrCodeValue")

            // 解析格式：BtMacAddress=597B004A0F53;WifiName=Geminic340dc;WifiPassword=88888888;WifiSecurity=wpa2-psk
            val pairs = qrCodeValue.split(";")
            val dataMap = mutableMapOf<String, String>()

            for (pair in pairs) {
                val keyValue = pair.split("=", limit = 2)
                if (keyValue.size == 2) {
                    dataMap[keyValue[0].trim()] = keyValue[1].trim()
                }
            }

            // 提取字段
            val btMacAddress = dataMap["BtMacAddress"]
            val wifiName = dataMap["WifiName"]
            val wifiPassword = dataMap["WifiPassword"]
            val wifiSecurity = dataMap["WifiSecurity"]

            // 验证必需字段
            if (btMacAddress.isNullOrEmpty()) {
                logE(TAG, "BtMacAddress is missing or empty in QR code")
                return null
            }

            // 格式化蓝牙MAC地址
            val formattedMacAddress = formatMacAddress(btMacAddress)
            if (formattedMacAddress == null) {
                logE(TAG, "Invalid MAC address format: $btMacAddress")
                return null
            }

            val qrCodeInfo = QRCodeInfo(
                btMacAddress = formattedMacAddress,
                wifiName = wifiName,
                wifiPassword = wifiPassword,
                wifiSecurity = wifiSecurity
            )

            logD(TAG, "Successfully parsed QR code: $qrCodeInfo")
            return qrCodeInfo

        } catch (e: Exception) {
            logE(TAG, "Failed to parse QR code value: $qrCodeValue", e)
            return null
        }
    }

    /**
     * 格式化MAC地址，添加冒号分隔符
     * @param macAddress 原始MAC地址（如：597B004A0F53）
     * @return 格式化后的MAC地址（如：59:7B:00:4A:0F:53），格式错误返回null
     */
    fun formatMacAddress(macAddress: String): String? {
        // 移除所有非十六进制字符
        val cleanMac = macAddress.replace(Regex("[^0-9A-Fa-f]"), "")

        // 验证长度是否为12个字符
        if (cleanMac.length != 12) {
            logE(TAG, "Invalid MAC address length: ${cleanMac.length}, expected 12")
            return null
        }

        // 验证是否都是有效的十六进制字符
        if (!cleanMac.matches(Regex("^[0-9A-Fa-f]{12}$"))) {
            logE(TAG, "Invalid MAC address format: contains non-hex characters")
            return null
        }

        // 添加冒号分隔符
        return cleanMac.chunked(2).joinToString(":")
    }

    /**
     * 验证MAC地址格式是否正确
     * @param macAddress MAC地址字符串
     * @return 是否为有效的MAC地址格式
     */
    fun isValidMacAddress(macAddress: String): Boolean {
        // MAC地址格式：XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX
        val macPattern = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
        return macAddress.matches(macPattern.toRegex())
    }
}

internal data class BeaconItem(var len: Int = 0, var type: Int = 0, var bytes: ByteArray? = null) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BeaconItem

        return bytes.contentEquals(other.bytes)
    }

    override fun hashCode(): Int {
        return bytes.contentHashCode()
    }
}

/**
 * 二维码信息数据类
 * @param btMacAddress 蓝牙MAC地址
 * @param wifiName WiFi名称
 * @param wifiPassword WiFi密码
 * @param wifiSecurity WiFi安全类型
 */
internal data class QRCodeInfo(
    val btMacAddress: String,
    val wifiName: String? = null,
    val wifiPassword: String? = null,
    val wifiSecurity: String? = null
)
