package com.link.riderservice.core.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach

/**
 * 使用Flow实现一个倒计时功能
 */
internal fun countDownByFlow(
    frequency: Int,
    interval: Long,
    scope: CoroutineScope,
    onTick: (Int) -> Unit,
    onFinish: (() -> Unit)? = null,
): Job {
    return flow {
        for (num in frequency downTo 0) {
            emit(num)
            if (num != 0) delay(interval)
        }
    }.flowOn(Dispatchers.IO)
        .onEach { onTick.invoke(it) }
        .onCompletion { cause -> if (cause == null) onFinish?.invoke() }
        .launchIn(scope) //保证在一个协程中执行
}