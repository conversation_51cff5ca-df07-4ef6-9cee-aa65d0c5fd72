package com.link.riderservice.core.extensions

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch


internal fun <T> MutableStateFlow<T>.setState(reducer: T.() -> T) {
    this.value = this.value.reducer()
}

internal fun <T> Flow<T>.collectWithScope(
    coroutineScope: CoroutineScope,
    collector: FlowCollector<T>
) {
    coroutineScope.launch {
        collect(collector)
    }
}