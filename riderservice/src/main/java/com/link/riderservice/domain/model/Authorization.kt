package com.link.riderservice.domain.model

import com.link.riderservice.core.utils.JSON
import com.link.riderservice.core.utils.JSONParser
import com.link.riderservice.core.utils.compareTo
import com.link.riderservice.core.utils.get

internal data class Authorization(
    var status: Int,
    var uuid: String
)

internal class AuthorizationParser : JSONParser<Authorization> {
    override fun parse(json: JSON): Authorization {
        val activate = Authorization(0, "")
        activate::status < json["status"]
        activate::uuid < json["uuid"]
        return activate
    }
}
