package com.link.riderservice.domain.model

import com.link.riderservice.core.utils.JSON
import com.link.riderservice.core.utils.JSONParser
import com.link.riderservice.core.utils.compareTo
import com.link.riderservice.core.utils.get

internal data class CheckInfo(
    var status: Int
)

internal class CheckInfoParser : JSONParser<CheckInfo> {
    override fun parse(json: JSON): CheckInfo {
        val checkInfo = CheckInfo(-1)
        checkInfo::status < json["status"]
        return checkInfo
    }
}

