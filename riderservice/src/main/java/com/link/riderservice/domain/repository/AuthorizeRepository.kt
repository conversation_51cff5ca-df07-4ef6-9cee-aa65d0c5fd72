package com.link.riderservice.domain.repository

import com.link.riderservice.domain.model.Authorization
import com.link.riderservice.domain.model.CheckInfo

internal interface AuthorizeRepository {
    suspend fun requestActivateStatus(
        key: String,
        mac: String,
        time: String,
        sign: String
    ): Result<Authorization>

    suspend fun requestCheckStatus(
        key: String,
        mac: String,
        uuid: String,
        time: String,
        licenseSign: String,
        sign: String
    ): Result<CheckInfo>
}